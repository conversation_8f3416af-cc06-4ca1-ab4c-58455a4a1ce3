<template>
  <el-row>
    <el-col :span="20">
      <div class="left">
        <div class="header">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="目标任务">
              <el-input
                v-model="formInline.zbrw"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="目标要求">
              <el-input
                v-model="formInline.mbyq"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="指标类型">
              <el-select v-model="formInline.zblx" placeholder="请选择">
                <el-option
                  v-for="item in zblxdata"
                  :key="item.guid"
                  :label="item.lxmc"
                  :value="item.lxmc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="onAllBtnClick" style="margin-right: 20px">
              全部
            </el-button>
          </el-form>
        </div>
        <div class="tabledata">
          <el-table
            ref="multipleTableRef"
            :data="tableData"
            style="width: 100%"
            :cell-style="columnStyle"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            highlight-current-row
            v-loading="loading"
            :row-key="
              (row: any) => {
                return row.guid;
              }
            "
          >
            <el-table-column width="40px" align="center" fixed>
              <template #default="{ row }">
                <el-radio v-model="radio" :label="row.guid"></el-radio>
              </template>
            </el-table-column>
            <el-table-column
              property="xyzp"
              label="部门自评"
              width="110"
              align="center"
            >
              <template #default="{ row }">
                <el-select
                  v-model="row.xyzp"
                  placeholder="请选择"
                  @visible-change="handleSelect(row)"
                  @change="selectValue(row)"
                  v-if="row.spzt !== '已完成' && row.spzt !== '审批中'"
                >
                  <el-option
                    v-for="item in XYPJData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div v-if="row.spzt == '已完成' || row.spzt == '审批中'">
                  {{ row.xyzp }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              property="name"
              label="学院完成情况"
              width="120"
              align="center"
            >
              <template #default="{ row }">
                <el-button
                  v-if="row.sfbmtxxyjck == '否' && row.dddd"
                  size="small"
                  type="danger"
                  @click="ckXYWCQK(row)"
                >
                  查看
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  v-if="row.sfbmtxxyjck == '是' && row.dddd"
                  @click="tbXYWCQK(row)"
                >
                  填报
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              property="zbrw"
              label="目标任务"
              sortable
              show-overflow-tooltip
              min-width="200px"
            >
              <template #default="{ row }">
                <el-tag
                  class="tag1"
                  v-if="row.sfbmtxxyjck == '是' && row.zrlx == '自主填报'"
                  style="border: none"
                  size="small"
                >
                  部门填
                </el-tag>
                <el-tag
                  class="tag2"
                  v-if="
                    row.wcqk !== null &&
                    row.wcqk !== '' &&
                    row.sfbmtxxyjck == '否' &&
                    row.zrlx == '自主填报'
                  "
                  style="border: none"
                  size="small"
                >
                  已填报
                </el-tag>
                <el-tag
                  class="tag3"
                  v-if="row.zrlx == '系统集成'"
                  style="border: none"
                  size="small"
                >
                  系统抽
                </el-tag>
                <el-tag
                  class="tag3"
                  v-if="row.hosttaskid !== null"
                  style="border: none"
                  size="small"
                >
                  协办
                </el-tag>
                <span>{{ row.zbrw }}</span>
              </template>
            </el-table-column>
            <el-table-column
              property="mbyq"
              label="目标要求"
              sortable
              show-overflow-tooltip
              min-width="300px"
            />
            <el-table-column
              property="zblx"
              label="指标类型"
              sortable
              align="center"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="ssbm"
              label="责任部门"
              sortable
              align="center"
              width="180"
              show-overflow-tooltip
            />
          </el-table>
        </div>
        <div class="right-page">
          <el-pagination
            v-model:current-page="page"
            v-model:page-size="limit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="total"
            class="page-fen"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-col>
    <el-col :span="4">
      <div class="left-header">
        <h2>目标完成情况</h2>
        <span>{{ currentClickRw }}</span>
      </div>
      <el-divider style="margin: 10px 0" />
      <el-scrollbar>
        <div v-if="eeee == '系统集成'">
          <h3 style="text-align: center; margin-bottom: 5px">
            本部门已完成：{{ currentCount }}项
          </h3>
          <span
            style="
              display: block;
              text-align: center;
              font-size: 12px;
              color: rgb(148, 148, 148);
            "
          >
            同步时间：{{ nowdataData }} 02:00:00
          </span>
          <div style="text-align: center">
            <router-link
              target="_blank"
              :to="{
                path: '/WCQK',
                query: { guid: rrrr.guid, depName: rrrr.ssbm },
              }"
              style="padding: 10px; color: #2a59b6; cursor: pointer"
            >
              查看完成情况
            </router-link>
          </div>
          <div class="bottom-plugin" style="padding: 10px; margin-top: 400px">
            <el-divider style="margin: 10px 0" />
            <h2>
              补充说明情况
              <el-button
                type="primary"
                style="margin-left: 5px"
                @click="onEnterInfo"
                v-if="bclrsmBtnShow"
              >
                录入
              </el-button>
            </h2>
            <el-divider style="margin: 10px 0" />
            <span>{{ delHtmlTag(currentBCSM) }}</span>
          </div>
        </div>
        <div class="aaaa" v-else>
          <div class="header-btn">
            <el-button
              type="primary"
              size="small"
              class="header-btn"
              @click="sendCompete"
              id="sendCompeteid"
              v-if="sbwcqkBtnShow"
            >
              上报完成情况
            </el-button>
          </div>
          <div class="connent" v-if="isShow">
            <span>修订时间：{{ currentWCS }}</span>
            <br />
            <span>自评结果：{{ currentClickXyzp }}</span>
            <p>{{ currentWCQK }}</p>
            <div class="fujian">
              <h3 v-if="currentFJ !== null">附件：</h3>
              <div class="fujian-plugin">
                <a
                  @click="onFy(item.uri)"
                  style="cursor: pointer"
                  v-for="(item, index) of currentFJ"
                  :key="index"
                >
                  {{ item.name }}
                </a>
              </div>
            </div>
            <div class="header-btn">
              <!-- <el-button type="primary" size="small" @click="checkPlugin">
                查看详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="editCompete"
                v-if="reapplyBtnShow"
                >修改</el-button
              > -->
            </div>
          </div>
          <!-- <div class="header-btn">
            <el-button
              type="warning"
              size="small"
              @click="checkLastYear"
              style="margin-top: 5px"
              v-if="qnShow"
            >
              查看去年填报情况
            </el-button>
          </div> -->
        </div>
      </el-scrollbar>
    </el-col>
  </el-row>
  <el-dialog
    v-model="dialogFormVisible"
    title="部门目标（各学院完成情况）"
    width="1450"
    hight="300px"
    destroy-on-close
  >
    <DialogPlugin :currentClick="currentClick" />
  </el-dialog>
  <el-dialog
    v-model="tbdialogFormVisible"
    title="部门目标（各学院完成情况）"
    width="1450"
    hight="300px"
    destroy-on-close
  >
    <tbDialog :tbcurrentClick="tbcurrentClick" />
  </el-dialog>
  <el-dialog
    title="目标完成情况填报"
    v-model="completeDialogVisible"
    width="1200"
    hight="500px"
    destroy-on-close
    class="completeDialog"
  >
    <iframe :src="iframeUrl" width="100%" height="600px"></iframe>
  </el-dialog>
  <el-dialog
    v-model="dialogEnterInfo"
    title="录入补充说明"
    width="600"
    hight="300"
    destroy-on-close
  >
    <el-input
      v-model="enterinfo"
      type="textarea"
      placeholder="请输入"
      :autosize="{ minRows: 5, maxRows: 20 }"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="editSubmit()">确定</el-button>
        <el-button @click="dialogEnterInfo = false">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  getRWFJZTById,
  getWCQKByRWId,
  getbrfzrwzbs,
  getzbshqk,
  getAllZNBMRW,
  getCurrentUserMenuPermission,
  checkReportTask,
  bmtxSaveEvaluate,
  getNowDate,
  getCurrentUserDepName,
  saveRWBCSM,
  getBCSMById,
  getRwmrfzr,
  getHQWCQK,
} from "@/api/TYJK/index";
import { getALLZBLX } from "@/api/MBXD/index";
import { onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive } from "vue";
import { useAuth } from "vue-oidc-provider";
import tbDialog from "./components/tbDialog.vue";
import DialogPlugin from "./components/DialogPlugin.vue";
import { XYPJSelect } from "@/utils/permissionConfig";
import axios from "axios";
import { useRoute } from "vue-router";

const radio = ref<boolean>(false);
const route = useRoute();
const { user } = useAuth();
const qnShow = ref(false);
const tbdialogFormVisible = ref<boolean>(false);
const sbwcqkBtnShow = ref<boolean>(false); //上报完成情况
const bclrsmBtnShow = ref<boolean>(false); //补充录入说明
const reapplyBtnShow = ref<boolean>(false); //修改
const iframeUrl = ref<string>("");
const multipleTableRef = ref();
const tbcurrentClick = ref();
const dialogEnterInfo = ref<boolean>(false);
const declarationShow = ref<boolean>(false);
// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
// 返回值改变颜色
const columnStyle = ({ columnIndex, row }: any) => {
  if (columnIndex == 1 && row.xyzp !== null) {
    return {
      color: "green",
    };
  }
  if (columnIndex == 1 && row.xyzp == null) {
    return {
      color: "red",
    };
  }
  if (
    (columnIndex == 2 && row.spzt == "已完成") ||
    (columnIndex == 2 && row.spzt == "审批中")
  ) {
    return {
      color: "blue",
    };
  }
};

const formInline = reactive<any>({
  zbrw: "",
  mbyq: "",
  xyzpfbzt: "",
  zblx: "",
  fzrxm: "",
});
const XYPJData = ref<any>({
  label: "",
  value: "",
});
const completeDialogVisible = ref<boolean>(false);
const isShow = ref<boolean>(false);
const currentWCS = ref<string>("");
const currentWCQK = ref<string | null>("");
const currentRWCount = ref<number>(0);
const currentFJ = ref({
  item: {
    name: "",
    uri: "",
  },
});
const eeee = ref();
const currentBCSM = ref<string | null>("");
const usercode = ref<number>(0);
const currentClickXyzp = ref<string>("");
const checkPluginData = ref();
const loading = ref<boolean>(false);
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const dialogFormVisible = ref(false);
const currentClick = ref();
const zblxdata = ref<GET_ALLZBLX_Type[]>([]);
const multipleSelection = ref<GET_RWFJZT_Type[]>([]);
const rrrr = ref();
const currentClickRw = ref<string>("");
const tableData = ref<GET_RWFJZT_Type[]>([]);
const zbshqk = ref<string>("");
const brfzrwzb = ref<string>("");
const onqbClick = ref<boolean>(false);
const handleSelectionChange = (val: GET_RWFJZT_Type[]) => {
  multipleSelection.value = val;
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  if (onqbClick.value == true) {
    onAllBtnClick();
  }
  if (onqbClick.value == false) {
    getCurrentYearDepartmentName();
  }
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  if (onqbClick.value == true) {
    onAllBtnClick();
  }
  if (onqbClick.value == false) {
    getCurrentYearDepartmentName();
  }
};

// 多选某些情况下禁用
const selectable = (row: any) => {
  if (
    row.hosttaskid !== null ||
    row.fzrxm !== user.value?.profile.UserName ||
    row.xyzpfbzt == "正在审批" ||
    row.xyzpfbzt == "已发布" ||
    row.zblx == "部门自拟指标" ||
    row.xyzp == "" ||
    row.xyzp == null
  ) {
    return false;
  } else {
    return true;
  }
};

const handleSelect = (row: any) => {
  XYPJSelect.forEach((item) => {
    if (row.zblx == item.zblx) {
      XYPJData.value = item.nzpj;
    }
  });
};

const selectValue = async (row: any) => {
  const res = await checkReportTask(row.guid);
  if (res === "未上报") {
    ElMessageBox.alert("评价失败，请先上报任务完成情况", "信息", {
      confirmButtonText: "确定",
    }).then(() => {
      row.xyzp = "";
    });
  } else {
    const { code } = await bmtxSaveEvaluate(row.guid, row.xyzp);
    if (code === 0) {
      ElMessage.success("编辑成功");
    }
  }
};

const enterinfo = ref();
const onEnterInfo = () => {
  dialogEnterInfo.value = true;
  enterinfo.value = JSON.parse(JSON.stringify(currentBCSM.value));
};

// 搜索
const searchData = async () => {
  try {
    loading.value = true;
    const currentyear = route.query.year;
    const requestInfo = {
      zbrw: formInline.zbrw,
      mbyq: formInline.mbyq,
      zblx: formInline.zblx,
    };
    const { data, count } = await getAllZNBMRW(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
      currentyear,
    );
    //控制查看按钮的显示与隐藏
    for await (const item of data) {
      item.dddd = await getXYWCQKHtml(item.guid, item.hosttaskid);
    }
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};
const getAllzblx = async () => {
  formInline.fzrxm = user.value?.profile.UserName;
  const { data } = await getALLZBLX();
  zblxdata.value = data;
};

const nowdataData = ref<string>("");
const getnowdate = async () => {
  const res = await getNowDate();
  nowdataData.value = res;
};

// 获取当前年度所有已发布任务
const getCurrentYearDepartmentName = async () => {
  try {
    loading.value = true;
    const currentyear = route.query.year;
    const currentssbm = route.query.ssbm;
    const requestInfo = {
      zbrw: formInline.zbrw,
      mbyq: formInline.mbyq,
      zblx: formInline.zblx,
      ssbm: currentssbm,
    };
    const { data, count } = await getAllZNBMRW(
      page.value,
      limit.value,
      JSON.stringify(requestInfo),
      currentyear,
    );
    //控制查看按钮的显示与隐藏
    for await (const item of data) {
      item.dddd = await getXYWCQKHtml(item.guid, item.hosttaskid);
    }
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const getXYWCQKHtml = async (guid: any, hosttaskid: any) => {
  if (hosttaskid) {
    return false;
  }
  const res = await getRWFJZTById(guid);
  if (res.count > 0) {
    // 查看按钮显示
    return true;
  }
  return false;
};

const ckXYWCQK = (row: any) => {
  currentClick.value = row;
  dialogFormVisible.value = true;
};

const tbXYWCQK = (row: any) => {
  tbcurrentClick.value = row;
  tbdialogFormVisible.value = true;
};

const currentParams = ref();
const currentCount = ref();
// 单击每一行任务
const handleRowClick = async (row: any) => {
  // console.log(row);

  if (row.oldtaskid !== null && row.oldtaskid !== "") {
    qnShow.value = true;
  } else {
    qnShow.value = false;
  }
  currentClickRw.value = row.zbrw;
  rrrr.value = row;
  eeee.value = row.zrlx;
  currentClickXyzp.value = row.xyzp;
  if (row.zrlx === "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
    if (user.value?.profile.UserId === row.fzrgh) {
      bclrsmBtnShow.value = true;
    } else {
      bclrsmBtnShow.value = false;
    }
  } else {
    const { data, count } = await getWCQKByRWId(row.guid);
    console.log(8888);

    if (data.length == 0) {
      isShow.value = false;
    } else {
      isShow.value = true;
    }
    // 自主填报
    if (row.zrlx == "自主填报" && row.hosttaskid == null) {
      sbwcqkBtnShow.value = true;
      if (row.wcqk == null || row.wcqk == "") {
        reapplyBtnShow.value = false;
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      } else {
        sbwcqkBtnShow.value = false;
        if (row.xyzpfbzt != "正在审批" && row.xyzpfbzt != "已发布") {
          if (row.fzrgh == user.value?.profile.UserId) {
            reapplyBtnShow.value = true;
          } else {
            reapplyBtnShow.value = false;
          }
        } else {
          reapplyBtnShow.value = false;
        }
      }
      if (row.hosttaskid == null) {
        if (row.fzrgh == user.value?.profile.UserId) {
          if (row.xyzpfbzt == "待发布" || row.xyzpfbzt == null) {
            reapplyBtnShow.value = true;
          } else {
            reapplyBtnShow.value = false;
          }
        }
      } else {
        reapplyBtnShow.value = false;
      }
      if (row.zblx == "部门自拟指标") {
        sbwcqkBtnShow.value = false;
      }
    } else {
      sbwcqkBtnShow.value = false;
    }
    checkPluginData.value = data[0];
    currentWCS.value = data[0].wcsj.slice(0, 10);
    currentWCQK.value = data[0].wcqk;
    currentFJ.value = JSON.parse(data[0].fj);
    currentRWCount.value = count;
  }
};

const onFy = (uri: string) => {
  const url = `https://ydmh.guangshaxy.com/formWebSite/form/${uri}`;
  window.open(url);
};

const sendCompete = () => {
  iframeUrl.value = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?MBRWGUID=${rrrr.value.guid}`;
  completeDialogVisible.value = true;
};

const getbrfzrwzb = async () => {
  const { data } = await getbrfzrwzbs();
  brfzrwzb.value = data;
};

const getzbshqks = async () => {
  const { data } = await getzbshqk(curdep.value);
  zbshqk.value = data;
};

// 获取当前登录人菜单权限
const getCurrentMenuPermission = async () => {
  // const { code } = await getCurrentUserMenuPermission();
  let res = await getCurrentUserMenuPermission();
  let code;
  if (window.sessionStorage.getItem("usercode") != null) {
    code = Number(window.sessionStorage.getItem("usercode"));
  } else {
    code = res[0].code;
  }
  usercode.value = code;
};

const onAllBtnClick = async () => {
  onqbClick.value = true;
  formInline.zbrw = "";
  formInline.zblx = "";
  formInline.mbyq = "";
  try {
    loading.value = true;
    const { data, count } = await getAllZNBMRW(
      page.value,
      limit.value,
      "",
      "2023",
    );
    //控制查看按钮的显示与隐藏
    for await (const item of data) {
      item.dddd = await getXYWCQKHtml(item.guid, item.hosttaskid);
    }
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const editSubmit = async () => {
  dialogEnterInfo.value = false;
  const { count } = await saveRWBCSM(rrrr.value.guid, enterinfo.value);
  if (count == 1) {
    ElMessage.success("录入补充说明成功！");
    currentBCSM.value = JSON.parse(JSON.stringify(enterinfo.value));
  }
};

const getMRFZR = async () => {
  const res = await getRwmrfzr();
  if (res === true) {
    declarationShow.value = true;
  } else {
    declarationShow.value = false;
  }
};

const checkLastYear = () => {
  window.open(
    `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${rrrr.value.oldtaskid}`,
  );
};

const pppp = ref();
const getRwmrfz = async () => {
  const res = await getRwmrfzr();
  pppp.value = res;
};

onMounted(async () => {
  await getCurrentUserDep();
  getAllzblx();
  await getRwmrfz();
  await getCurrentYearDepartmentName();
  getbrfzrwzb();
  await getzbshqks();
  getnowdate();
  getCurrentMenuPermission();

  getMRFZR();
});
</script>

<style lang="less" scoped>
h2 {
  text-align: center;
}
.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 100px);
}
.left-header {
  text-align: center;
}
.header-btn {
  text-align: center;
  margin-bottom: 5px;
}
.connent {
  padding: 10px;
  p {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  span {
    color: rgb(148, 148, 148);
  }
}
.header {
  height: 40px;
  .el-form {
    display: flex;
    // justify-content: space-between;
  }
}

.center {
  float: right;
  margin-right: 80px;
}
.tabledata {
  margin: 2px 20px 10px 0px;
}
.el-row {
  padding: 15px 20px 15px 20px;
}
.fujian {
  margin-top: 10px;
  margin-bottom: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 200px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}
/* 更改背景色 */
/deep/.el-popper {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}
.page-fen {
  margin-left: 150px;
}

.el-table {
  height: calc(100vh - 195px);
  border: 1px solid rgb(236, 238, 244);
}
.el-scrollbar {
  height: calc(100vh - 215px);
}
/deep/ .el-radio__label {
  display: none;
}
iframe {
  border: none;
}
/deep/.el-dialog__body {
  padding: 0 20px 20px 20px;
}
.center-btn {
  display: flex;
  justify-content: space-between;
  margin-right: 20px;
  margin-bottom: 10px;
  .btn-left {
    .top {
      margin-top: 6px;
      margin-bottom: 10px;
      span {
        color: #919398;
        margin-right: 30px;
      }
    }
  }
  .btn-right {
    padding-top: 20px;
  }
}
.tag1 {
  background: orange;
  color: #fff;
  margin-right: 5px;
}
.tag2 {
  background: green;
  color: #fff;
  margin-right: 5px;
}

.tag3 {
  background: #3896fb;
  color: #fff;
  margin-right: 5px;
}
</style>

<!--操作手册-->
<template>
  <div class="operating-manual">
    <h3>一网通考核系统-帮助系统v1.0-beta</h3>
    <p>发展规划处</p>
    <el-divider />
    <h4>联系我们:</h4>
    <div class="header-content">
      <div class="item">
        <div class="img">
          <img src="../../assets/imges/cz1.png" alt="" />
        </div>
        <div class="info">
          <h3>
            吴旻
            <span style="font-size: 12px; margin-left: 5px; color: #7f8490">
              （发展规划处）
            </span>
          </h3>
          <p>负责：系统操作等问题</p>
          <p>电话：8821</p>
        </div>
      </div>
      <div class="item">
        <div class="img">
          <img src="../../assets/imges/cz1.png" alt="" />
        </div>
        <div class="info">
          <h3>
            宗亚赏
            <span style="font-size: 12px; margin-left: 5px; color: #7f8490">
              （杭州一多信息技术有限公司）
            </span>
          </h3>
          <p>负责：系统问题、操作、使用、流程等问题</p>
          <p>电话：15132092967</p>
        </div>
      </div>
    </div>
    <h4 style="margin-top: 20px">资料下载：</h4>
    <div class="material">
      <div class="items" v-for="(item, index) of material" :key="index">
        <h3>{{ item.title }}</h3>
        <p @click="handleDownload(item.url)">点击下载</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

type Material = {
  title: string;
  url: string;
};

const material = ref<Array<Material>>([
  {
    title: "学院版、教学部版操作手册v1.0",
    url: "http://***********:8008/form/attachments/files/wj/操作手册v1.0学院版、教学部版.pdf",
  },
  {
    title: "机关部门版操作手册v1.0",
    url: "http://***********:8008/form/attachments/files/wj/操作手册v1.0机关部门版.pdf",
  },
  {
    title: "问题解答（待完善）",
    url: "https://ydmh.guangshaxy.com/gateway_base/form/attachments/files/wj/%E4%B8%80%E7%BD%91%E9%80%9A%E7%B3%BB%E7%BB%9F%E9%97%AE%E9%A2%98%E8%A7%A3%E7%AD%94.docx",
  },
]);

const handleDownload = (url: string) => {
  window.open(url, "_blank");
};
</script>

<style lang="less" scoped>
.operating-manual {
  background: #eef0f1;
  height: 100vh - 6px;
  padding: 20px;
  p {
    margin-top: 10px;
    font-size: 12px;
    color: #7f8490;
  }
  .header-content {
    display: flex;
    margin-top: 20px;
    .item {
      display: flex;
      width: 50%;
      height: 200px;
      background: #fff;
      margin-right: 10px;
      border-radius: 10px;
      border: 1px solid #eee;
      .img {
        width: 180px;
        height: 180px;
      }
      .info {
        margin-left: 20px;
        padding: 10px;
      }
    }
  }
  .material {
    display: flex;
    margin-top: 20px;
    .items {
      width: 50%;
      height: 100px;
      background: #fff;
      margin-right: 10px;
      border-radius: 10px;
      padding: 20px;
      border: 1px solid #eee;
      p {
        color: #87a6d7;
        cursor: pointer;
      }
    }
  }
}
</style>

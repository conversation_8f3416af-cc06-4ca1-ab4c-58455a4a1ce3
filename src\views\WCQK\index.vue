<template>
  <h1 style="text-align: center; font-size: 40px">{{ currentZb }}</h1>
  <el-table
    class="my-table"
    :data="tableData"
    style="width: 100%; border-bottom: 0"
    :header-cell-style="{
      backgroundColor: '#ebeaea',
      color: '#000',
      fontSize: '25px',
    }"
  >
    <el-table-column
      v-for="(item, index) in dynamicHaderArr"
      :key="index"
      :label="item.name"
      :value="item.code"
      :prop="item.code"
    />
  </el-table>
</template>

<script setup lang="ts">
import { getHQWCQK } from "@/api/TYJK/index";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useAuth } from "vue-oidc-provider";
import axios from "axios";

const { user } = useAuth();
const tableData = ref([]);
const route = useRoute();
const currentGuid = ref();
const currentDep = ref();
const currentParams = ref();
const dynamicHaderArr = ref([
  {
    name: "",
    code: "",
  },
]);
const currentDTBT = ref();
const currentZb = ref();
const getTableHeader = async () => {
  currentGuid.value = route.query.guid;
  currentDep.value = route.query.depName;
  const res1 = await getHQWCQK(currentGuid.value, currentDep.value);
  currentZb.value = res1.msg;
  currentParams.value = res1.data;
  currentDTBT.value = res1.data.split("$")[0];
  // 动态表头
  const res2 = await axios.get(
    `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourceConfigbySqlNo?sqlNo=${currentDTBT.value}`,
    {
      headers: {
        Authorization: `Bearer ${user.value?.access_token}`,
      },
    },
  );
  dynamicHaderArr.value = res2.data.data.fieldConfigs.fields;
  // 动态表格数据
  const res3 = await axios.get(
    `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
    {
      headers: {
        Authorization: `Bearer ${user.value?.access_token}`,
      },
    },
  );
  tableData.value = res3.data.data;
};
onMounted(() => {
  getTableHeader();
});
</script>

<style lang="less" scoped>
.el-table {
  padding: 0px 10px 10px 10px;
}
.my-table {
  border: none;
}
</style>

<!--学院考核-->
<template>
  <div v-if="reload">
    <el-row>
      <!--      <el-col :span="4">-->
      <!--        <h2>学院目标任务</h2>-->
      <!--        <el-divider style="margin: 10px 0" />-->
      <!--        <div class="scroll-container">-->
      <!--          <div class="scroll-content" v-scroll>-->
      <!--            <el-tree-->
      <!--              :data="treeData"-->
      <!--              :props="defaultProps"-->
      <!--              @node-click="handleNodeClick"-->
      <!--              highlight-current-->
      <!--            />-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </el-col>-->
      <el-col :span="20">
        <div class="header">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <!-- <el-form-item label="一级指标">
              <el-select
                v-model="formInline.yjzb"
                placeholder="请选择"
                clearable
                changeYjzb
                @change="(val:string) => changeYjzb(val)"
              >
                <el-option
                  v-for="item in currentYjzb"
                  :key="item.guid"
                  :label="item.yjzb"
                  :value="item.yjzb"
                />
              </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="二级指标">
              <el-select
                v-model="formInline.ejzb"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in currentEJZB"
                  :key="item.guid"
                  :value="item.ejzb"
                  :label="item.ejzb"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="指标等级">
              <el-input
                v-model="formInline.sjzb"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <!-- <el-form-item label="下达部门">
              <el-select
                v-model="formInline.xdbm"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in xdbmData"
                  :key="item.departmentid"
                  :label="item.departmentname"
                  :value="item.departmentname"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="searchData">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button @click="getAllRW">全部</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="center">
          <!-- <div class="text">
            <span
              >“系统评价”仅针对从业务系统抽取数据的指标项，默认为最低评价</span
            >
          </div> -->
          <div class="btn">
            <!-- <el-button type="primary" @click="handleReport">提交评价</el-button> -->
            <el-button @click="exportExcel">导出</el-button>
          </div>
        </div>
        <div class="tabledata">
          <el-table
            :data="tableData"
            style="width: 100%"
            v-loading="loading"
            :row-key="(row: any) => row.guid"
            @row-click="handleRowClick"
            highlight-current-row
          >
            <el-table-column width="50px" align="center" fixed>
              <template #default="{ row }">
                <el-radio v-model="radio" :label="row.guid"></el-radio>
              </template>
            </el-table-column>
            <el-table-column
              prop="bmgxypj"
              label="最终评价"
              align="center"
              show-overflow-tooltip
              width="120"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.bmgxypj"
                  placeholder="请输入数字"
                  @change="(val: number) => onBmgxypjInput(val, row)"
                />
              </template>
            </el-table-column>

            <el-table-column
              prop="xyzp"
              label="自评分数"
              align="center"
              show-overflow-tooltip
              width="90"
            />
            <!-- <el-table-column
              prop="sjzbfz"
              label="分值"
              align="center"
              show-overflow-tooltip
              width="90"
            >
            </el-table-column> -->
            <!-- <el-table-column
              property="yjzb"
              label="一级指标"
              sortable
              show-overflow-tooltip
              min-width="110px"
            >
            </el-table-column>
            <el-table-column
              property="ejzb"
              label="二级指标"
              sortable
              show-overflow-tooltip
              min-width="120px"
            >
            </el-table-column> -->
            <el-table-column
              prop="sjzb"
              label="指标等级"
              sortable
              show-overflow-tooltip
              min-width="140px"
            >
              <template #default="{ row }">
                <el-tag
                  v-if="row.hosttaskid !== null"
                  style="border: none"
                  size="small"
                >
                  协办
                </el-tag>
                <el-tag
                  v-if="row.zrlx == '系统集成'"
                  style="border: none"
                  size="small"
                >
                  系统抽
                </el-tag>
                <span>{{ row.zbrw }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="ssbm"
              label="责任部门"
              sortable
              width="150"
              show-overflow-tooltip
            />
            <el-table-column
              prop="mbyq"
              label="考核内容及标准"
              sortable
              show-overflow-tooltip
              min-width="150px"
            />
            <!--            <el-table-column-->
            <!--              prop="ssmk"-->
            <!--              label="所属模块"-->
            <!--              sortable-->
            <!--              width="130"-->
            <!--              show-overflow-tooltip-->
            <!--            />-->
            <el-table-column
              prop="xdbm"
              label="下达部门"
              sortable
              show-overflow-tooltip
              width="130"
            />
          </el-table>
        </div>
        <div class="right-page">
          <el-pagination
            v-model:current-page="page"
            v-model:page-size="limit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="total"
            class="page-fen"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
      <el-col :span="4">
        <h2>目标完成情况</h2>
        <el-divider style="margin: 10px 0" />
        <el-scrollbar>
          <div v-if="currentBCSM !== ''">
            <div style="text-align: center">
              <h3 style="text-align: center; margin-bottom: 5px">
                本部门已完成：{{ currentCount }}项
              </h3>
              <router-link
                target="_blank"
                :to="{
                  path: '/WCQK',
                  query: { guid: rowInfo.guid, depName: rowInfo.ssbm },
                }"
                style="padding: 10px; color: #2a59b6; cursor: pointer"
              >
                查看完成情况
              </router-link>
            </div>
            <div class="bottom-plugin">
              <el-divider style="margin: 10px 0" />
              <h2>补充说明情况</h2>
              <el-divider style="margin: 10px 0" />
              <span>{{ delHtmlTag(currentBCSM) }}</span>
            </div>
          </div>
          <div class="aaaa" v-else>
            <div class="header-btn" style="margin-left: 10px">
              <!-- <el-button
                type="primary"
                size="small"
                class="header-btn"
                @click="sendCompete"
                id="sendCompeteid"
                v-if="sbwcqkBtnShow"
              >
                上报完成情况
              </el-button> -->
            </div>
            <div class="connent" v-for="item in currentRWData" :key="item.guid">
              <span>修订时间：{{ item.sqsj }}</span>
              <br />
              <span>完成情况：{{ item.wcqk }}</span>
              <!-- <p>{{ item.wcqk }}</p> -->
              <!-- <el-button
                size="small"
                type="primary"
                style="margin-top: 10px"
                @click="checkPlugin(item.guid)"
                v-if="item.wcqk !== null"
                >查看详情
              </el-button> -->
              <div class="fujian" v-if="fjshow">
                <h3>附件：</h3>
                <div class="fujian-plugin">
                  <a
                    @click="onFy(item.url)"
                    style="cursor: pointer"
                    v-for="(item, index) of item.fj"
                    :key="index"
                  >
                    {{ item.name }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </el-col>
    </el-row>
    <el-dialog
      v-model="exportVisible"
      title="选择导出字段"
      width="500px"
      hight="300px"
      destroy-on-close
    >
      <el-checkbox-group v-model="checkList">
        <el-checkbox
          v-for="(item, index) in exportZDList"
          :key="index"
          :label="item.value"
          size="large"
        >
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="exportSumbit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  getRwkallnd,
  getRwzblxBynd,
  getCOLLEGETASK,
  getWCQKByRWId,
  getBCSMById,
  getCurrentUserDepName,
  saveBmYearEndEvaluate,
  getHQWCQK,
  getBBMEJZB,
  getBBMSSMK,
  getAllDep,
  saveWCQK,
  getBMYJZB,
} from "@/api/TYJK/index";
import { getEJXYByND } from "@/api/BMKH/index";
import { getALLZBLX } from "@/api/MBXD/index";
import { getCurYear } from "@/utils";
import { XYPJSelect } from "@/utils/permissionConfig";
import { ElMessage } from "element-plus";
import { reactive, ref, onMounted, nextTick } from "vue";
import { useAuth } from "vue-oidc-provider";
import axios from "axios";

const { user } = useAuth();
const fjshow = ref(false);
// const columnStyle = ({ columnIndex, row }: any) => {
//   if (
//     (columnIndex == 2 && row.bmgxypj !== null && row.bmgxypjfbzt == "已发布") ||
//     (columnIndex == 3 && row.xyzpfbzt == "已发布" && row.xyzp !== null)
//   ) {
//     return {
//       color: "green",
//     };
//   }
//   if (
//     (columnIndex == 2 && row.bmgxypj == null && row.bmgxypjfbzt !== "已发布") ||
//     (columnIndex == 3 && row.xyzp == null)
//   ) {
//     return {
//       color: "red",
//     };
//   }
//   if (row.xyzpfbzt !== "已发布" && columnIndex == 3) {
//     return {
//       color: "red",
//     };
//   }
//   if (row.bmgxypjfbzt !== "已发布" && columnIndex == 2) {
//     return {
//       color: "red",
//     };
//   }
// };
// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
const checkList = ref([
  "nzpj",
  "xyzp",
  "zbrw",
  "mbyq",
  "zbly",
  "zblx",
  "bmgxypj",
  "ssbm",
  "xbbm",
  "wcqk",
  "fj",
]);
const exportZDList = [
  {
    label: "学校评价",
    value: "nzpj",
  },
  {
    label: "部门评价",
    value: "bmgxypj",
  },
  {
    label: "学院自评",
    value: "xyzp",
  },
  {
    label: "目标任务",
    value: "zbrw",
  },
  {
    label: "目标要求",
    value: "mbyq",
  },
  {
    label: "指标来源",
    value: "zbly",
  },
  {
    label: "指标类型",
    value: "zblx",
  },
  {
    label: "下达部门",
    value: "xdbm",
  },
  {
    label: "责任部门",
    value: "ssbm",
  },
  {
    label: "有无附件",
    value: "fj",
  },
  {
    label: "完成情况",
    value: "wcqk",
  },
];
const exportVisible = ref<boolean>(false);
const reload = ref(true);
const currentBCSM = ref<string | null>("");
const currentRWData = ref<any[]>([]);
const rowInfo = ref();
const radio = ref<boolean>(false);
const ssbm = ref("");
const nd = ref("");
const zblx = ref("");
const currentYear = ref<number>(0);
const loading = ref<boolean>(false);
const sbwcqkBtnShow = ref<boolean>(false); //上报完成情况
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const type = ref<string>("");
const selectSsbm = ref();
const zblxdata = ref<GET_ALLZBLX_Type[]>([]);
const formInline = reactive({
  yjzb: "",
  ejzb: "",
  sjzb: "",
  xdbm: "",
  zblx: "",
  zbrw: "",
  mbyq: "",
});
const tableData = ref<GET_ALLZNBMRW_Type[]>([]);
interface DD {
  name: string;
  bmid: string;
  datatype: string;
  nd: string;
  px: number;
  ssbm: string;
  children?: any[] | undefined;
}

interface Tree {
  nd: string;
  name: string;
  children?: DD[];
}
const defaultProps = {
  children: "children",
  label: "name",
};
const treeData = ref<Tree[]>([]);
const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const changeYjzb = async (val: string) => {
  const res = await getBBMEJZB(val);
  currentEJZB.value = res.data;
};

// 搜索
const searchData = async () => {
  try {
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      xdbm: formInline.xdbm,
      ssbm: selectSsbm.value ? selectSsbm.value.ssbm : "",
      nd: selectSsbm.value ? selectSsbm.value.nd : "",
    };
    loading.value = true;
    const res = await getCOLLEGETASK(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
    );
    total.value = res.count;
    tableData.value = res.data;
  } finally {
    loading.value = false;
  }
};

// 提交评价跳转
const handleReport = async () => {
  const res = await getCurrentUserDepName();
  const url = `https://ehall.zcmu.edu.cn:5003/iTask/Process210825144828?type=二级学院&xdbm=${res}`;
  window.open(url);
};

// 获取左侧树形数据
// const handleNodeClick = (data: GET_ZRBM_BYND_Type, nodeValue: any) => {
//   selectSsbm.value = data;
//   const year = data.nd;
//   treeData.value.filter(async (item) => {
//     if (item.nd === year) {
//       if (!("bmid" in data)) {
//         const { data: result } = await getEJXYByND(data.nd);
//         item.children = result.filter((item) => (item.name = item.ssbm));
//       }
//       const id = data.bmid;
//       const ssbm = data.ssbm;
//       // 得到第三层数据
//       item.children.filter(async (ele) => {
//         if (ele.bmid === id) {
//           if (!("zblx" in data)) {
//             const { data: result2 } = await getRwzblxBynd(data.nd, data.bmid);
//             ele.children = result2.filter((ele) => {
//               ele.name = ele.zblx;
//               ele.ssbm = ssbm;
//               return ele;
//             });
//           }
//         }
//       });
//     }
//   });
//   if (!data.bmid) {
//     return;
//   }
//   if (data.px) {
//     zblx.value = "";
//     ssbm.value = data.ssbm;
//     nd.value = data.nd;
//     getclickndrw();
//     return;
//   }
//   ssbm.value = nodeValue.parent.data.name;
//   nd.value = data.nd;
//   zblx.value = data.zblx;
//   formInline.zblx = data.name;
//   getclickndrw();
// };

// 根据筛选渲染表格数据
const getclickndrw = async () => {
  try {
    loading.value = true;
    if (zblx.value === "") {
      zblx.value = formInline.zblx;
    }
    const { data, count } = await getCOLLEGETASK(
      page.value,
      limit.value,
      JSON.stringify({ ...formInline, ssbm: ssbm.value, zblx: zblx.value }),
      nd.value,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const getcurrentallbmrw = async () => {
  try {
    loading.value = true;
    const { data, count } = await getCOLLEGETASK(page.value, limit.value);
    total.value = count;
    tableData.value = data;
  } finally {
    loading.value = false;
  }
};

// 获取到全部的任务
const getAllRW = async () => {
  try {
    loading.value = true;
    const { data, count } = await getCOLLEGETASK(
      (page.value = 1),
      (limit.value = 15),
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  getclickndrw();
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  getclickndrw();
};

// 获取指标类型
const getAllzblx = async () => {
  const { data } = await getALLZBLX();
  zblxdata.value = data;
};

// 查询年度
const getAllnd = async () => {
  const { data } = await getRwkallnd();
  treeData.value = data.filter(async (item) => {
    const { data: result } = await getEJXYByND(item.nd);
    if (result !== null) {
      item.children = result.filter((item: any) => (item.name = item.ssbm));
    } else {
      item.children = [
        {
          bmid: "",
          datatype: "",
          name: "",
          nd: "",
          px: 0,
          ssbm: "",
        },
      ];
    }
    reload.value = false;
    nextTick(() => {
      reload.value = true;
    });
    return (item.name = item.nd);
  });
};

const currentParams = ref();
const currentCount = ref();
// 点击表格某一行获取目标任务
const handleRowClick = async (row: any) => {
  radio.value = row.guid;
  rowInfo.value = row;
  currentBCSM.value = "";
  currentRWData.value = [];
  if (row.zrlx == "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
  } else {
    const { data } = await getWCQKByRWId(row.guid);
    // 自主填报
    if (row.zrlx == "自主填报" && row.hosttaskid == null) {
      if (row.wcqk == null || row.wcqk == "") {
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      } else {
        sbwcqkBtnShow.value = false;
      }
      if (row.zblx == "部门自拟指标") {
        sbwcqkBtnShow.value = false;
      }
    } else {
      sbwcqkBtnShow.value = false;
    }
    // 学校仅查看
    if (row.sfbmtxxyjck == "是") {
      sbwcqkBtnShow.value = false;
    } else if (row.sfbmtxxyjck !== "是" && row.zrlx === "自主填报") {
      if (row.wcqk == null || row.wcqk == "") {
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      }
    }
    currentRWData.value = data.map((item) => {
      item.sqsj = item.sqsj.slice(0, 10);
      if (item.fj) {
        fjshow.value = true;
        item.fj = JSON.parse(item.fj);
      }
      return item;
    });
  }
};

const onBmgxypjInput = async (val: any, row: any) => {
  const res = await saveWCQK([row]);
  if (res.code === 200 && res.count === 1) {
    ElMessage.success("编辑成功");
  }
};

const onFy = (uri: string) => {
  const url = uri;
  window.open(url);
};

// 目标情况里的查看详情
const checkPlugin = (guid: any) => {
  const url = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${guid}`;
  window.open(url);
};

// 导出
const exportExcel = () => {
  exportVisible.value = true;
};

// 导出确定
const exportSumbit = () => {
  const obj = {};
  checkList.value.forEach((item: any) => {
    obj[item] = true;
  });
  const url = `http://10.18.0.224:7005/api/RWDC/ExportDepAssessmentTargetTask?dczd=${JSON.stringify(
    [obj],
  )}&type=学院&departmentName=${
    selectSsbm.value ? selectSsbm.value.ssbm : curdep.value
  }
  &zbrw=${formInline.zbrw}&mbyq=${formInline.mbyq}&zblx=${formInline.zblx}`;
  console.log(url);

  window.open(url);
  exportVisible.value = false;
};

const sendCompete = () => {
  const obj = new Function("return" + rowInfo.value.zbly)();
  const url = `https://ehall.zcmu.edu.cn:5003/iTask/Process210607153632?ZBRWGUID=${rowInfo.value.guid}&ZBRW=${rowInfo.value.zbrw}&MBYQ=${rowInfo.value.mbyq}&ZBLX=${rowInfo.value.zblx}&ZBLY=${obj[0].name}&BZ=${rowInfo.value.bz}&SJRW=${rowInfo.value.sjrw}`;
  window.open(url);
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
};

const currentEJZB = ref();
const getCurrentEjzb = async () => {
  const res = await getBBMEJZB("");
  currentEJZB.value = res.data;
};

const currentSsmk = ref();
const getcurrentSsmk = async () => {
  const res = await getBBMSSMK();
  currentSsmk.value = res.data;
};

const xdbmData = ref<GET_COLLEGEINFO_Type[]>([]);
const getAllDepe = async () => {
  const { data } = await getAllDep();
  xdbmData.value = data;
};

onMounted(async () => {
  getAllnd();
  getAllzblx();
  getcurrentallbmrw();
  currentYear.value = await getCurYear();
  getCurrentUserDep();
  getAllDepe();
  getCurrentEjzb();
  getcurrentSsmk();
  getcurrentyjzb();
});
</script>

<style lang="less" scoped>
h2 {
  text-align: center;
}
.left-header {
  text-align: center;
  h3 {
    color: #7a7a7a;
    font-weight: 400;
  }
}
.bottom-plugin {
  padding: 10px;
  margin-top: 200px;
}
h4 {
  padding: 10px;
  color: #2a59b6;
}
.connent {
  padding: 10px;
  border-bottom: 1px solid #dddfe5;
  p {
    margin-top: 10px;
  }
}
.connent:last-child {
  border-bottom: none;
}
.scroll-container {
  width: 100%;
  overflow-x: scroll;
}

.scroll-content {
  display: flex;
  white-space: nowrap;
  height: calc(100vh - 180px); /* 设置容器高度 */
  overflow-y: auto; /* 设置垂直方向滚动条 */
}

/* 可以添加滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #c1c1c1;
}
.header {
  margin: 0px 10px;
  height: 45px;
  .el-form {
    display: flex;
  }
}
.center {
  float: right;
  margin: 0px 10px 10px 10px;
  .text {
    padding-top: 10px;
    span {
      color: rgb(148, 148, 148);
      text-align: center;
    }
  }
}
.tabledata {
  margin: 12px 10px 10px 10px;
}
.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 100px);
}
.el-tag {
  background: #5a9cf8;
  color: #fff;
  margin-right: 5px;
}
.page-fen {
  margin-left: 150px;
}
.el-table {
  height: calc(100vh - 215px);
  border: 1px solid rgb(236, 238, 244);
}
.el-row {
  padding: 15px 20px 15px 20px;
}
/* 更改背景色 */
/deep/.el-popper {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}
/deep/ .el-radio__label {
  display: none;
}
.el-scrollbar {
  height: calc(100vh - 180px);
}
/deep/.el-dialog__body {
  padding: 0 20px 20px 20px;
}
.fujian {
  margin-top: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 200px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}
</style>

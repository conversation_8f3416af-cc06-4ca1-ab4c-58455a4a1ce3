{"name": "target-manager", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@8.15.5", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "prettier --write ."}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vue-office/docx": "1.6.0", "@vue-office/excel": "1.6.0", "@vue-office/pdf": "1.6.0", "@vueuse/core": "^10.1.2", "axios": "^1.4.0", "echarts": "5.6.0", "element-plus": "^2.3.4", "less": "^4.1.3", "normalize.css": "^8.0.1", "oidc-client-ts": "2.0.6", "pnpm": "^8.15.5", "prettier": "^3.4.1", "vue": "^3.2.47", "vue-oidc-provider": "0.0.2", "vue-router": "^4.2.0"}, "devDependencies": {"@types/node": "^20.1.5", "@vitejs/plugin-vue": "^4.1.0", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.24.1", "vite": "^4.3.2", "vue-tsc": "^1.4.2"}}
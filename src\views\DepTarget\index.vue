<!--目标下达-->
<template>
  <div v-loading="elLoading" element-loading-background="rgba(122, 122, 122, 0.8)" element-loading-custom-class="loadingClass">
    <el-row>
      <el-col :span="4">
        <div class="scroll-container">
          <div class="scroll-content" v-scroll>
            <!-- 左侧树形 -->
            <el-tree :data="treeData" :props="defaultProps" @node-click="handleNodeClick" highlight-current
              expand-on-click-node />
          </div>
        </div>
      </el-col>
      <el-col :span="20">
        <div class="header">
          <!-- 头部搜索栏 -->
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="年度">
              <el-select v-model="year" @change="handleChange">
                <el-option label="2025" value="2025"></el-option>
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2023" value="2023"></el-option>
                <el-option label="2022" value="2022"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审批状态">
              <el-select v-model="formInline.zbshzt" placeholder="请选择">
                <el-option label="全部" value="全部" />
                <el-option label="未审核" value="未审核" />
                <el-option label="审批中" value="审核中" />
                <el-option label="已审核" value="已审核" />
              </el-select>
            </el-form-item>
            <el-form-item label="下派状态">
              <el-select v-model="formInline.dqzt" placeholder="请选择">
                <el-option label="全部" value="全部" />
                <el-option label="未下派" value="待发布" />
                <el-option label="审批中" value="正在审批中" />
                <el-option label="已下派" value="已发布" />
              </el-select>
            </el-form-item>
            <el-form-item label="考核内容及标准">
              <el-input v-model="formInline.zbrw" placeholder="请输入" @keyup.enter="searchData" />
            </el-form-item>
            <el-form-item label="指标解释">
              <el-input v-model="formInline.mbyq" placeholder="请输入" @keyup.enter="searchData" />
            </el-form-item>
            <el-form-item label="指标类型">
              <el-select v-model="formInline.zblx" placeholder="请选择">
                <el-option v-for="item in zblxdata" :key="item.guid" :label="item.lxmc" :value="item.lxmc"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchData">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button @click="getAllRW">全部</el-button>
            </el-form-item>
          </el-form>
          <div class="center-btn">
            <div class="left">
              <!-- 中间按钮 -->
              <el-button type="primary" size="small" @click="setData()">
                添加
              </el-button>
              <el-button type="danger" size="small" @click="deleteRWData">
                批量删除
              </el-button>
              <el-button type="warning" round size="small" @click="setTask(currentYear - 1)">
                {{ currentYear - 1 }}
              </el-button>
              <el-button type="primary" round size="small" @click="setTask(currentYear - 2)">
                {{ currentYear - 2 }}
              </el-button>
              <el-button type="info" round size="small" @click="setTask('更多')">
                更多...
              </el-button>
            </div>

            <div class="right">
              <el-button type="primary" size="small" @click="importVisible = true">
                导入
              </el-button>
              <el-button type="primary" size="small" @click="exportVisible = true">
                导出
              </el-button>
              <el-button type="primary" size="small" class="last-btn" @click="sentCheckLc">
                指标审核
              </el-button>
              <el-button type="primary" size="small" class="last-btn" @click="sentCurrentRw">
                发布当年任务
              </el-button>
            </div>
          </div>

          <!-- 所有职能部门指标类型表格 -->
          <div class="table-connent">
            <el-table ref="multipleTableRef" :data="tableData" style="width: 100%"
              @selection-change="handleSelectionChange" v-loading="tableloading">
              <el-table-column type="selection" width="55" align="center"></el-table-column>
              <el-table-column property="zt" label="审核状态" width="120" sortable>
                <template #default="{ row }">
                  <el-tag :style="getSpTagStyle(row.zbshzt)" style="border: none">
                    {{ getSpTagContent(row.zbshzt) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column property="zt" label="下派状态" width="120" sortable>
                <template #default="{ row }">
                  <el-tag :style="getTagStyle(row.dqzt)" style="border: none">
                    {{ getTagContent(row.dqzt) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column property="zbrw" label="考核内容及标准" sortable show-overflow-tooltip>
                <template #default="{ row }">
                  <el-tag v-if="row.hosttaskid !== null" style="border: none">
                    协办
                  </el-tag>
                  <span>{{ row.zbrw }}</span>
                </template>
              </el-table-column>
              <el-table-column property="mbyq" label="指标解释" sortable show-overflow-tooltip>
                <template #default="{ row }">
                  <span v-if="row !== editingRow" @click="editCell(row)">
                    {{ row.mbyq }}
                  </span>
                  <el-input v-else size="small" v-model="row.mbyq" @blur="saveEdit(row)" @keyup.enter="saveEdit(row)" />
                </template>
              </el-table-column>
              <el-table-column property="zblx" label="指标类型" sortable align="center" width="150" show-overflow-tooltip />
              <el-table-column property="ssbm" label="责任部门" sortable align="center" width="200" show-overflow-tooltip />
              <el-table-column label="操作" width="150" align="center">
                <template #default="{ row }">
                  <el-button size="small" type="primary" @click="setData(row)">
                    编辑
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteRowData(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="right-page">
              <!-- 底部分页 -->
              <el-pagination :current-page="page" :page-size="limit" :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
                layout=" prev, pager, next, jumper,total, sizes" :total="total" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" class="page-fen" />
              <el-button class="footer-btn">确定</el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 添加编辑复用弹框 -->
    <el-dialog @close="dialogFormVisible = false" v-model="dialogFormVisible" :close-on-click-modal="false"
      :before-close="handleClose" :title="form.guid ? '编辑部门任务' : '添加部门任务'" width="850px">
      <DialogPlugin v-if="dialogFormVisible" :form="form" :dialogVisibleSubmit="dialogFormVisible"
        @onDialogVisibleSubmit="dialogSubmit" />
    </el-dialog>
    <!-- 穿梭框 -->
    <el-dialog v-model="dialogShuttleFrame" destroy-on-close :close-on-click-modal="false" class="dialogShuttle">
      <ShuttleFrame :year="clickButtonYear" />
    </el-dialog>

    <el-dialog v-model="exportVisible" title="选择导出字段" width="500px" hight="300px" destroy-on-close>
      <el-checkbox-group v-model="checkList">
        <el-checkbox v-for="(item, index) in exportZDList" :key="index" :label="item.value" size="large">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="exportSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="importVisible" title="数据导入" width="500">
      <div class="upload-box">
        <el-button type="primary" @click="handleDawnLoad">模版下载</el-button>
        <el-upload class="upload-demo" action="" accept=".xls,.xlsx" :show-file-list="false"
          :http-request="httpRequest">
          <el-button type="success">选择文件</el-button>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  getAllZNBMRW,
  deleteRW,
  EditMbyq,
  getBMYJZB,
  getCurrentUserMenuPermission,
  getCurrentUserDepName,
} from "@/api/TYJK/index";
import { getALLZBLX, getAllBmzblx, uploadFile } from "@/api/MBXD/index";
import ShuttleFrame from "./components/ShuttleFrame.vue";
import DialogPlugin from "./components/DialogPlugin.vue";
import { reactive, ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getCurYear } from "@/utils";
import { UploadRequestOptions } from "element-plus/es/components";
const zblxdata = ref<GET_ALLZBLX_Type[]>([]);
const deleteSelectData = ref<GET_ADDUPDATERW_Type[]>([]);
const tableloading = ref<boolean>(false);
const currentYear = ref<number>(0);
const clickButtonYear = ref<number>(0);
const editingRow = ref(null);
interface Form {
  guid: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  zbly: string;
  ssbm: string;
  sflhzb: string;
  bmssmbzrlx: string;
  zrlx: string;
  xbbm: string;
  bz: string;
  hxzb: string;
  sfbmtxxyjck: string;
  lhzbgz: string;
}
const dialogShuttleFrame = ref(false);
const dialogFormVisible = ref(false);
const year = ref<string>("2024");
const exportVisible = ref<boolean>(false);
const usercode = ref<number>(0);
const type = ref<string>("");
const elLoading = ref<boolean>(false);
const importVisible = ref(false);
const checkList = ref([
  "spzt",
  "sjzbfz",
  "fzrxm",
  "yjzb",
  "ejzb",
  "sjzb",
  "mbyq",
  "zbrw",
  "zblx",
  "ssbm",
  "xbbm",
  "wcqk",
  "fj",
]);
const exportZDList = [
  {
    label: "评分",
    value: "spzt",
  },
  {
    label: "指标等级",
    value: "sjzb",
  },
  {
    label: "目标任务",
    value: "zbrw",
  },
  {
    label: "评分方法",
    value: "mbyq",
  },
  {
    label: "负责人",
    value: "fzrxm",
  },
  {
    label: "责任部门",
    value: "ssbm",
  },
  {
    label: "协办部门",
    value: "xbbm",
  },
  {
    label: "完成情况",
    value: "wcqk",
  },
  {
    label: "有无附件",
    value: "fj",
  },
];

type TreeData = {
  name: string;
  children: GET_ZNBM_ZBLX_Type[];
};
const treeData = ref<TreeData[]>([
  { name: "职能部门", children: [] },
  { name: "指标类型", children: [] },
]);

const defaultProps = {
  children: "children",
  label: "name",
};
const form = ref<Form>({
  guid: "",
  zbrw: "",
  mbyq: "",
  zblx: "",
  zbly: "",
  ssbm: "",
  bmssmbzrlx: "",
  zrlx: "",
  xbbm: "",
  bz: "",
  hxzb: "",
  sflhzb: "",
  sfbmtxxyjck: "",
  lhzbgz: "",
});

const handleClose = () => {
  dialogFormVisible.value = false;
  form.value = {
    guid: "",
    zbrw: "",
    mbyq: "",
    zblx: "",
    zbly: "",
    ssbm: "",
    bmssmbzrlx: "",
    zrlx: "",
    xbbm: "",
    bz: "",
    hxzb: "",
    sflhzb: "",
    sfbmtxxyjck: "",
    lhzbgz: "",
  };
};

//审批状态设置
const getSpTagStyle = (type: string) => {
  if (type === "未审核") {
    return {
      background: "#F56c6c",
      color: "white",
    };
  } else if (type === "已审核") {
    return {
      background: "#429488",
      color: "white",
    };
  } else {
    return;
  }
};
const getSpTagContent = (type: string) => {
  if (type === "未审核") {
    return "未审核";
  } else if (type === "已审核") {
    return "已审核";
  } else {
    return "审核中";
  }
};

//状态列样式和数据转换
const getTagStyle = (type: string) => {
  if (type === "待发布") {
    return {
      background: "#F56c6c",
      color: "white",
    };
  } else if (type === "已发布") {
    return {
      background: "#429488",
      color: "white",
    };
  } else {
    return;
  }
};
const getTagContent = (type: string) => {
  if (type === "待发布") {
    return "未下派";
  } else if (type === "已发布") {
    return "已下派";
  } else {
    return "审批中";
  }
};

const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const tableData = ref<GET_ALLZNBMRW_Type[]>([]);
const formInline = reactive({
  zbrw: "",
  mbyq: "",
  zblx: "",
  zbshzt: "",
  dqzt: "",
  ssbm: "",
  yjzb: "",
  ejzb: "",
  sjzb: "",
  fzrxm: "",
  xyzpfbzt: "",
});
const handleSelectionChange = (val: GET_ADDUPDATERW_Type[]) => {
  deleteSelectData.value = val;
};

const handleDawnLoad = () => {
  window.open(
    "https://ydmh.guangshaxy.com/gateway_base/form/attachments/files/20250701/一网通导入模板.xlsx", "_blank"
  );
  importVisible.value = false;
};

const httpRequest = async (options: UploadRequestOptions) => {
  console.log(options);
  elLoading.value = true;
  importVisible.value = false;
  const formData = new FormData();
  formData.append('file', options.file);
  try {
    const res = await uploadFile(formData);
    if (res.code === 1) {
      ElMessage.success('上传成功');
      // 刷新数据 
      getcurrentallbmrw(); 
    } else {
      ElMessage.error(res.msg || '上传失败');
    }
  } catch (error) {
    ElMessage.error('上传异常');
  } finally {
    elLoading.value = false;

  }
};

// 获取指标类型
const getAllzblx = async () => {
  const { data } = await getALLZBLX();
  zblxdata.value = data;
};
// 顶部搜索
const searchData = async () => {
  try {
    const requestInfo = {
      zbrw: formInline.zbrw,
      mbyq: formInline.mbyq,
      zbshzt: formInline.zbshzt,
      dqzt: formInline.dqzt,
      zblx: formInline.zblx,
      ssbm: selectSsbm.value ? selectSsbm.value.name : "",
    };
    tableloading.value = true;
    const res = await getAllZNBMRW(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
      currentYear.value,
    );
    total.value = res.count;
    tableData.value = res.data;
  } finally {
    tableloading.value = false;
  }
};
const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  getTableData();
};
const handleSizeChange = (num: number) => {
  limit.value = num;
  getTableData();
};

//指标审核流程
const sentCheckLc = () => {
  const url = "https://ydmh.guangshaxy.com/formWebSite/iTask/Process240329093914";
  window.open(url);
};
// 发布当年任务
const sentCurrentRw = () => {
  const url = "https://ydmh.guangshaxy.com/formWebSite/iTask/Process240403095810";
  window.open(url);
};

const selectSsbm = ref();
// 点击左侧获取对应筛选数据
const handleNodeClick = async (data: GET_ZNBM_ZBLX_Type) => {
  selectSsbm.value = data;
  try {
    if (data?.children) {
      return;
    }
    if (data.datatype === "职能部门") {
      formInline.ssbm = data.name;
    } else {
      formInline.zblx = data.name;
      selectSsbm.value = "";
    }
    page.value = 1;
    getTableData();
  } finally {
  }
};

// 获取表格数据
const getTableData = async () => {
  try {
    tableloading.value = true;
    const res = await getAllZNBMRW(
      page.value,
      limit.value,
      JSON.stringify(formInline),
      currentYear.value,
    );
    total.value = res.count;
    tableData.value = res.data;
  } finally {
    tableloading.value = false;
  }
};

// 获取左面树形职能/指标数据
const getBmzblx = async () => {
  const { data } = await getAllBmzblx();
  if (data) {
    data.filter((item) => {
      if (item.datatype == "职能部门") {
        treeData.value[0].children.push(item);
      } else {
        treeData.value[1].children.push(item);
      }
    });
  }
};

const getcurrentallbmrw = async () => {
  try {
    tableloading.value = true;
    const { data, count } = await getAllZNBMRW(page.value, limit.value, "", "");
    total.value = count;
    tableData.value = data;
  } finally {
    tableloading.value = false;
  }
};

// 控制添加编辑数据是否回显
const setData = async (row: any = null) => {
  // 先关闭弹窗
  dialogFormVisible.value = false;

  // 重置表单数据
  form.value = {
    guid: "",
    zbrw: "",
    mbyq: "",
    zblx: "",
    zbly: "",
    ssbm: "",
    bmssmbzrlx: "",
    zrlx: "",
    xbbm: "",
    bz: "",
    hxzb: "",
    sflhzb: "",
    sfbmtxxyjck: "",
    lhzbgz: "",
  };

  // 如果是编辑模式，则设置表单数据
  if (row) {
    form.value = { ...row };
  }

  // 打开弹窗
  dialogFormVisible.value = true;
};

// 获取到全部的任务
const getAllRW = async () => {
  try {
    tableloading.value = true;
    const { data, count } = await getAllZNBMRW(
      (page.value = 1),
      (limit.value = 15),
      "",
      currentYear.value,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    tableloading.value = false;
    (formInline.zbshzt = ""),
      (formInline.dqzt = ""),
      (formInline.mbyq = ""),
      (formInline.ssbm = ""),
      (formInline.zblx = ""),
      (formInline.zbrw = "");
  }
};

const deleteRWData = () => {
  ElMessageBox.confirm("确定删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      Promise.all(
        deleteSelectData.value.map((item) => {
          return deleteRW(item.guid as string);
        }),
      ).then(() => {
        ElMessage.success("批量删除成功");
        getcurrentallbmrw();
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "删除已取消",
      });
    });
};

// 单条数据删除任务
const deleteRowData = (row: any) => {
  ElMessageBox.confirm("确定删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const res = deleteRW(row.guid);
      ElMessage.success("删除成功");
      getcurrentallbmrw();
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "删除已取消",
      });
    });
};

const dialogSubmit = () => {
  dialogFormVisible.value = false;
  getcurrentallbmrw();
};

// 控制202x弹框
const setTask = (type: any) => {
  clickButtonYear.value = type;
  dialogShuttleFrame.value = true;
};

const editCell = (row: any) => {
  editingRow.value = row;
};

const saveEdit = async (row: any) => {
  editingRow.value = null;
  const { code } = await EditMbyq(row.guid, row.mbyq);
  if (code === 200) {
    ElMessage.success("编辑成功");
  }
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
};

const currentEJZB = ref();
const getCurrentEjzb = async () => {
  const res = await getBBMEJZB("");
  currentEJZB.value = res.data;
};

const handleChange = (value: string) => {
  if (value !== "2024") {
    tableData.value = [];
  } else {
    getTableData();
  }
};

const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const exportSubmit = () => {
  const obj = {};
  checkList.value.forEach((item: any) => {
    obj[item] = true;
  });
  if (usercode.value == 20 || usercode.value == 30 || usercode.value == 50) {
    type.value = "部门";
  } else {
    type.value = "学院";
  }
  const url = `http://10.18.0.224:7005/api/RWDC/ExportAllCollegeTargetTask?dczd=${JSON.stringify(
    [obj],
  )}&departmentName=${curdep.value}&type=${type.value}
  &yjzb=${formInline.yjzb}&ejzb=${formInline.ejzb}&sjzb=${formInline.sjzb
    }&fzrxm=${formInline.fzrxm}&xyzpfbzt=${formInline.xyzpfbzt}`;
  console.log(url);
  window.open(url);
  exportVisible.value = false;
};

const getCurrentMenuPermission = async () => {
  let res = await getCurrentUserMenuPermission();
  let code;
  if (window.sessionStorage.getItem("usercode") != null) {
    code = Number(window.sessionStorage.getItem("usercode"));
  } else {
    code = res[0].code;
  }
  usercode.value = code;
};

onMounted(async () => {
  getCurrentMenuPermission();
  getcurrentallbmrw();
  getBmzblx();
  getAllzblx();
  currentYear.value = await getCurYear();
});
</script>

<style lang="less" scoped>
.scroll-container {
  width: 100%;
  overflow-x: scroll;
}

.scroll-content {
  display: flex;
  white-space: nowrap;
  height: calc(100vh - 100px);
  /* 设置容器高度 */
  overflow-y: auto;
  /* 设置垂直方向滚动条 */
}

/* 可以添加滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #c1c1c1;
}

.header {
  margin-left: 20px;

  .el-form {
    display: flex;
    justify-content: space-between;
    height: 45px;

    .el-form-item {
      .el-select .el-input__wrapper {
        width: 100px;
      }
    }
  }

  .center-btn {
    display: flex;
    justify-content: space-between;
  }

  .table-connent {
    margin-top: 15px;
  }

  .right-page {
    display: flex;

    .page-fen {
      // width: 100px;
      margin-top: 10px;
    }

    .footer-btn {
      margin-top: 10px;
      margin-left: 20px;
    }
  }
}

.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 100px);
}

.el-tag {
  background: #5a9cf8;
  color: #fff;
}

:deep(.el-dialog__body) {
  padding: 5px 10px 20px 10px;
}

:deep(.dialogShuttle) {
  width: 1400px;
}

.el-table {
  height: calc(100vh - 215px);
  border: 1px solid rgb(236, 238, 244);
}

.el-row {
  padding: 15px 20px 15px 20px;
}

/* 更改背景色 */
:deep(.el-popper) {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 25%;
  line-height: 24px;
  border: none;
}

.upload-box {
  display: flex;
  justify-content: center;
  gap: 50px;
  //  padding: 50px 0;
  padding-top: 30px;
  padding-bottom: 20px;

  .upload-demo {
    margin-left: 20px;

  }
}
.loadingClass{
  z-index: 99999;
}
</style>

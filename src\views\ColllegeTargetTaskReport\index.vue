<!--学院考评-->
<template>
  <el-row>
    <el-col :span="20">
      <div class="left">
        <div class="header">
          <el-form :inline="true" :model="formInline">
            <!-- <el-form-item label="一级指标">
              <el-select
                v-model="formInline.yjzb"
                placeholder="请选择"
                clearable
                @change="(val:string) => changeYjzb(val)"
              >
                <el-option
                  v-for="item in currentYjzb"
                  :key="item.guid"
                  :label="item.yjzb"
                  :value="item.yjzb"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="二级指标">
              <el-select
                v-model="formInline.ejzb"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in currentEJZB"
                  :key="item.guid"
                  :value="item.ejzb"
                  :label="item.ejzb"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="年度">
              <el-select v-model="year" @change="handleChange">
                <el-option label="2025" value="2025"></el-option>
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2023" value="2023"></el-option>
                <el-option label="2022" value="2022"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="指标等级">
              <el-input
                v-model="formInline.sjzb"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="部门任务负责人：">
              <el-input
                v-model="formInline.fzrxm"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="部门/学院">
              <el-select
                v-model="formInline.ssbm"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="(item, index) in bmxyData"
                  :key="index"
                  :label="item.ssbm"
                  :value="item.ssbm"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="学院">
              <el-select v-model="formInline.ssbm" placeholder="请选择">
                <el-option
                  v-for="item in XYData"
                  :key="item.departmentid"
                  :label="item.departmentname"
                  :value="item.departmentname"
                />
              </el-select>
            </el-form-item> -->
            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="onAllBtnClick" style="margin-right: 20px">
              全部
            </el-button>
          </el-form>
        </div>
        <div class="center-btn">
          <!-- <el-button type="primary" @click="submitEvaluate">提交评价</el-button> -->
          <el-tooltip
            content="默认导出本人指标，若需导出所有指标，请先点击“全部”按钮"
            effect="light"
          >
            <el-button @click="exportExcel">导出</el-button>
          </el-tooltip>
        </div>
        <div class="center"></div>
        <div class="tabledata">
          <el-table
            ref="multipleTableRef"
            :data="tableData"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            v-loading="loading"
            highlight-current-row
          >
            <el-table-column
              type="selection"
              width="30"
              fixed
              :selectable="selectable"
            />

            <el-table-column
              label="指标锁定"
              align="center"
              show-overflow-tooltip
              width="90"
            >
              <template #default="{ row }">
                <el-button
                  :type="row.sfyc == '是' ? 'danger' : 'primary'"
                  @click="toggleLock(row)"
                >
                  {{ row.sfyc == "是" ? "解锁" : "锁定" }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column label="部门评价" width="110" align="center">
              <template #default="{ row }">
                <el-input
                  v-model="row.bmgxypj"
                  placeholder="请输入"
                  @change="(val: number) => onChangeInput(val, row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              property="xyzp"
              label="自评分数"
              width="110"
              align="center"
            ></el-table-column>
            <!-- <el-table-column
              property="sjzbfz"
              label="分值"
              width="110"
              align="center"
            /> -->
            <!-- <el-table-column
              property="yjzb"
              label="一级指标"
              sortable
              show-overflow-tooltip
              min-width="120px"
            >
            </el-table-column>
            <el-table-column
              property="ejzb"
              label="二级指标"
              sortable
              show-overflow-tooltip
              min-width="150px"
            >
            </el-table-column> -->
            <el-table-column
              property="sjzb"
              label="指标等级"
              min-width="150"
              sortable
              show-overflow-tooltip
            />

            <!--            <el-table-column-->
            <!--                property="xdbm"-->
            <!--                label="下达部门"-->
            <!--                align="center"-->
            <!--                sortable-->
            <!--                show-overflow-tooltip-->
            <!--                width="130"-->
            <!--            />-->

            <el-table-column
              property="sjzb"
              label="考核内容及标准"
              min-width="150"
              sortable
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <el-tag
                  class="tag3"
                  v-if="row.zrlx == '系统集成'"
                  style="border: none"
                  size="small"
                >
                  系统抽
                </el-tag>
                <span>{{ row.zbrw }}</span>
              </template>
            </el-table-column>

            <el-table-column
              property="mbyq"
              label="评分办法"
              sortable
              width="300"
              show-overflow-tooltip
            />

            <el-table-column
              property="ssbm"
              label="学院"
              sortable
              width="150"
              show-overflow-tooltip
            />
            <el-table-column
              property="fzrxm"
              label="负责人"
              sortable
              width="120"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column
              property="bmrwfzrxm"
              label="部门任务负责人"
              sortable
              width="150"
              show-overflow-tooltip
              align="center"
            />
            <!--            <el-table-column-->
            <!--              property="ssmk"-->
            <!--              label="所属模块"-->
            <!--              sortable-->
            <!--              width="150"-->
            <!--              align="center"-->
            <!--              show-overflow-tooltip-->
            <!--            />-->
            <!--            <el-table-column-->
            <!--              property="xdbm"-->
            <!--              label="下达部门"-->
            <!--              align="center"-->
            <!--              sortable-->
            <!--              show-overflow-tooltip-->
            <!--              width="130"-->
            <!--            />-->
          </el-table>
        </div>
        <div class="right-page">
          <el-pagination
            v-model:current-page="page"
            v-model:page-size="limit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="total"
            class="page-fen"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-col>
    <el-col :span="4">
      <div class="left-header">
        <h2>目标完成情况</h2>
        <span>{{ currentClickRw }}</span>
      </div>
      <el-divider style="margin: 10px 0" />
      <el-scrollbar>
        <div v-if="eeee == '系统集成'">
          <h3 style="text-align: center; margin-bottom: 5px">
            本部门已完成：{{ currentCount }}项
          </h3>
          <span
            style="
              display: block;
              text-align: center;
              font-size: 12px;
              color: rgb(148, 148, 148);
            "
          >
            同步时间：{{ nowdataData }} 02:00:00
          </span>
          <div style="text-align: center">
            <router-link
              target="_blank"
              :to="{
                path: '/WCQK',
                query: { guid: rrrr.guid, depName: curdep },
              }"
              style="padding: 10px; color: #2a59b6; cursor: pointer"
            >
              查看完成情况
            </router-link>
          </div>
          <div class="bottom-plugin" style="padding: 10px; margin-top: 400px">
            <el-divider style="margin: 10px 0" />
            <h2>
              补充说明情况
              <el-button
                type="primary"
                style="margin-left: 5px"
                @click="onEnterInfo"
                v-if="bclrsmBtnShow"
              >
                录入
              </el-button>
            </h2>
            <el-divider style="margin: 10px 0" />
            <span>{{ delHtmlTag(currentBCSM) }}</span>
          </div>
        </div>
        <div class="aaaa" v-else>
          <div class="header-btn">
            <!-- <el-button
              type="primary"
              size="small"
              class="header-btn"
              @click="sendCompete"
              id="sendCompeteid"
              v-if="sbwcqkBtnShow"
            >
              上报完成情况
            </el-button> -->
          </div>
          <div class="content" v-if="isShow">
            <p class="content-item">
              修订时间：{{ checkPluginData?.sqsj.slice(0, 10) }}
            </p>
            <p class="content-item">完成情况：{{ checkPluginData?.wcqk }}</p>
            <p class="content-item">下载次数：{{ checkPluginData?.xzcs }}</p>
            <div class="fujian" v-if="fjshow">
              <h3>附件：</h3>
              <div class="fujian-plugin">
                <!--                <a-->
                <!--                  @click="onFy(item.url)"-->
                <!--                  style="cursor: pointer"-->
                <!--                  v-for="(item, index) of currentFJ"-->
                <!--                  :key="index"-->
                <!--                  >{{ item.name }}</a-->
                <!--                >-->
                <div v-for="(fjItem, index) in currentFJ" :key="index">
                  {{ fjItem.name }}
                  <el-button size="small" @click="onFy(fjItem.url)">
                    预览
                  </el-button>
                  <el-button
                    size="small"
                    @click="downloadAttachment(fjItem.url, fjItem.name)"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <div class="header-btn">
              <!-- <el-button type="primary" size="small" @click="checkPlugin">
                查看详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="editCompete"
                v-if="reapplyBtnShow"
                >修改</el-button
              > -->
            </div>
          </div>
          <!-- <div class="header-btn">
            <el-button
              type="warning"
              size="small"
              @click="checkLastYear"
              style="margin-top: 5px"
              v-if="qnShow"
            >
              查看去年填报情况
            </el-button>
          </div> -->
        </div>
      </el-scrollbar>
    </el-col>
  </el-row>
  <el-dialog
    v-model="exportVisible"
    title="选择导出字段"
    width="500px"
    hight="300px"
    destroy-on-close
  >
    <el-checkbox-group v-model="checkList">
      <el-checkbox
        v-for="(item, index) in exportZDList"
        :key="index"
        :label="item.value"
        size="large"
      >
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="exportSumbit">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dialogEnterInfo"
    title="录入补充说明"
    width="600"
    hight="300"
    destroy-on-close
  >
    <el-input
      v-model="enterinfo"
      type="textarea"
      placeholder="请输入"
      :autosize="{ minRows: 5, maxRows: 20 }"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="editSubmit()">确定</el-button>
        <el-button @click="dialogEnterInfo = false">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  getXYKPAllCollegeTargetTask,
  getAllCollegeInfo,
  getCurrentUserDepName,
  getRwmrfzr,
  getNowDate,
  getBBMEJZB,
  getBBMSSMK,
  getAllDep,
  saveRWBCSM,
  getWCQKByRWId,
  getBCSMById,
  getHQWCQK,
  saveWCQK,
  getBMXY,
  getBMYJZB,
  updataYCZT,
  getNZPJ,
} from "@/api/TYJK/index";
import { onMounted } from "vue";
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useAuth } from "vue-oidc-provider";
import axios from "axios";

import { useRoute } from "vue-router";
import { updateXZCS } from "@/api/BMKH";
const route = useRoute();

const eeee = ref();
const currentBCSM = ref<string | null>("");
const currentClickXyzp = ref<string>("");
const checkPluginData = ref<GET_RWQK_Type>();
const rrrr = ref();
const dialogEnterInfo = ref<boolean>(false);
const declarationShow = ref<boolean>(false);
const currentClickRw = ref<string>("");
const { user } = useAuth();
const qnShow = ref(false);
const isShow = ref<boolean>(false);
const currentRWCount = ref<number>(0);
const sbwcqkBtnShow = ref<boolean>(false); //上报完成情况
const bclrsmBtnShow = ref<boolean>(false); //补充录入说明
const reapplyBtnShow = ref<boolean>(false); //修改
const formInline = reactive({
  yjzb: "",
  ejzb: "",
  sjzb: "",
  ssbm: "",
  fzrxm: "",
  xdbm: "",
  ssmk: "",
});
const fjshow = ref(false);
const year = ref<string>("2024");

// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};

const checkList = ref([
  "bmgxypj",
  "xyzp",
  "zbrw",
  "mbyq",
  "zbly",
  "zblx",
  "ssbm",
  "xdbm",
  "xbbm",
  "wcqk",
  "fj",
]);
const exportZDList = [
  {
    label: "部门评价",
    value: "bmgxypj",
  },
  {
    label: "学院自评",
    value: "xyzp",
  },
  {
    label: "目标任务",
    value: "zbrw",
  },
  {
    label: "目标要求",
    value: "mbyq",
  },
  {
    label: "指标来源",
    value: "zbly",
  },
  {
    label: "指标类型",
    value: "zblx",
  },
  {
    label: "责任部门",
    value: "ssbm",
  },
  {
    label: "下达部门",
    value: "xdbm",
  },
  {
    label: "协办部门",
    value: "xbbm",
  },
  {
    label: "完成情况",
    value: "wcqk",
  },
  {
    label: "有无附件",
    value: "fj",
  },
];
const currentFJ = ref<{ id: string; name: string; uri: string; url: string }[]>(
  [],
);
const iframeUrl = ref<string>("");
const pppp = ref();
const multipleTableRef = ref();
const useDep = ref<string>("");
const exportVisible = ref<boolean>(false);
const XYData = ref<GET_COLLEGEINFO_Type[]>([]);
const multipleSelection = ref<GET_RWFJZT_Type[]>([]);
const loading = ref<boolean>(false);
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const tableData = ref<GET_RWFJZT_Type[]>([]);
const onqbClick = ref<boolean>(false);
const completeDialogVisible = ref<boolean>(false);
// 多选某些情况下禁用
const selectable = (row: any) => {
  if (
    row.hosttaskid !== null ||
    row.bmrwfzrxm !== user.value?.profile.UserName ||
    row.bmgxypjfbzt == "正在审批" ||
    row.bmgxypjfbzt == "已发布"
  ) {
    return false;
  } else {
    return true;
  }
};

// 切换指标锁定状态
const toggleLock = (row) => {
  if (row.sfyc == "否") {
    row.sfyc = "是";
  } else {
    row.sfyc = "否";
  }
  // 在这里可以添加其他逻辑，比如发送API请求等
  updataYCZT(row.guid, row.sfyc);
};

const handleSelectionChange = (val: GET_RWFJZT_Type[]) => {
  multipleSelection.value = val;
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  if (onqbClick.value === true) {
    onAllBtnClick();
  }
  if (onqbClick.value === false) {
    if (searchClick.value == true) {
      cxbgData();
    }
    if (searchClick.value == false) {
      if (formInline.fzrxm !== user.value?.profile.UserName) {
        cxbgData();
      } else {
        getCurrentYearRWByCollege();
      }
    }
  }
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  if (onqbClick.value === false) {
    if (searchClick.value == true) {
      cxbgData();
    }
    if (searchClick.value == false) {
      if (formInline.fzrxm !== user.value?.profile.UserName) {
        cxbgData();
      } else {
        getCurrentYearRWByCollege();
      }
    }
  }
  if (onqbClick.value === true) {
    onAllBtnClick();
  }
};

const searchClick = ref(false);
const searchData = async () => {
  searchClick.value = true;
  onqbClick.value = false;
  try {
    loading.value = true;
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      fzrxm: formInline.fzrxm,
      xdbm: formInline.xdbm,
      ssbm: formInline.ssbm,
    };
    const { data, count } = await getXYKPAllCollegeTargetTask(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
      "",
      route.query.code as string,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const cxbgData = async () => {
  try {
    loading.value = true;
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      fzrxm: formInline.fzrxm,
      xdbm: formInline.xdbm,
      ssbm: formInline.ssbm,
    };
    const { data, count } = await getXYKPAllCollegeTargetTask(
      page.value,
      limit.value,
      JSON.stringify(requestInfo),
      "",
      route.query.code as string,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const getCollegeInfo = async () => {
  const { data } = await getAllCollegeInfo();
  XYData.value = data;
};

const changeYjzb = async (val: string) => {
  const res = await getBBMEJZB(val);
  currentEJZB.value = res.data;
};

const getCurrentYearRWByCollege = async () => {
  try {
    loading.value = true;
    if (pppp.value == true) {
      const { data, count } = await getXYKPAllCollegeTargetTask(
        page.value,
        limit.value,
        "",
        "",
        route.query.code as string,
      );
      tableData.value = data;
      total.value = count;
    } else {
      const requestInfo = {
        yjzb: formInline.yjzb,
        ejzb: formInline.ejzb,
        sjzb: formInline.sjzb,
        xdbm: formInline.xdbm,
        ssbm: formInline.ssbm,
        fzrxm: formInline.fzrxm,
      };
      const { data, count } = await getXYKPAllCollegeTargetTask(
        page.value,
        limit.value,
        JSON.stringify(requestInfo),
        "",
        route.query.code as string,
      );
      tableData.value = data;
      total.value = count;
    }
  } finally {
    loading.value = false;
  }
};

// 获取当前登陆人的部门名称
const getCurrentUserDepname = async () => {
  const res = await getCurrentUserDepName();
  useDep.value = res;
};

const currentParams = ref();
const currentCount = ref();
// 单击每一行任务
const handleRowClick = async (row: any) => {
  // console.log(row);

  if (row.oldtaskid !== null && row.oldtaskid !== "") {
    qnShow.value = true;
  } else {
    qnShow.value = false;
  }
  currentClickRw.value = row.zbrw;
  rrrr.value = row;
  eeee.value = row.zrlx;
  currentClickXyzp.value = row.xyzp;
  if (row.zrlx === "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
    if (user.value?.profile.UserId === row.fzrgh) {
      bclrsmBtnShow.value = true;
    } else {
      bclrsmBtnShow.value = false;
    }
  } else {
    const { data, count } = await getWCQKByRWId(row.guid);

    if (data.length == 0) {
      isShow.value = false;
    } else {
      isShow.value = true;
    }
    // 自主填报
    if (row.zrlx == "自主填报" && row.hosttaskid == null) {
      sbwcqkBtnShow.value = true;
      if (row.wcqk == null || row.wcqk == "") {
        reapplyBtnShow.value = false;
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      } else {
        sbwcqkBtnShow.value = false;
        if (row.xyzpfbzt != "正在审批" && row.xyzpfbzt != "已发布") {
          if (row.fzrgh == user.value?.profile.UserId) {
            reapplyBtnShow.value = true;
          } else {
            reapplyBtnShow.value = false;
          }
        } else {
          reapplyBtnShow.value = false;
        }
      }
      if (row.hosttaskid == null) {
        if (row.fzrgh == user.value?.profile.UserId) {
          if (row.xyzpfbzt == "待发布" || row.xyzpfbzt == null) {
            reapplyBtnShow.value = true;
          } else {
            reapplyBtnShow.value = false;
          }
        }
      } else {
        reapplyBtnShow.value = false;
      }
      if (row.zblx == "部门自拟指标") {
        sbwcqkBtnShow.value = false;
      }
    } else {
      sbwcqkBtnShow.value = false;
    }
    checkPluginData.value = data[0];
    if (data[0]?.fj) {
      fjshow.value = true;
      currentFJ.value = JSON.parse(data[0].fj);
    } else {
      fjshow.value = false;
    }
    currentRWCount.value = count;
  }
};

// 修改数字
const onChangeInput = async (val: any, row: any) => {
  const res = await getNZPJ(row.guid, row.bmgxypj);
  if (res.code === 200 && res.count === 1) {
    ElMessage.success("编辑成功");
  } else {
    ElMessage.error(res.msg);
  }
};

// const onFy = (uri: string) => {
//   const url = uri;
//   window.open(url);
// };
const onFy = (uri: string) => {
  const url = uri;
  // window.open(url);
  handOnlineView(url);
};

// handOnlineView 在线查看
const handOnlineView = (file: string) => {
  const onlineViewFile = (url: string) => {
    if (url) {
      // 获取文件格式
      const getFileFormatFun = (getFileFormatFunUrl: string) => {
        const fileFormatStr = getFileFormatFunUrl.slice(
          getFileFormatFunUrl.lastIndexOf(".") + 1,
        );
        return fileFormatStr;
      };

      // if (getFileFormatFun(url) === 'pdf') {
      if (["pdf", "png", "jpg"].includes(getFileFormatFun(url))) {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", url);
        xhr.responseType = "blob";
        xhr.onload = () => {
          console.log(206666, xhr);
          if (xhr.status === 200) {
            let blobType = "";
            if (getFileFormatFun(url) === "pdf") {
              blobType = "application/pdf";
            }
            if (["png", "jpg"].includes(getFileFormatFun(url))) {
              blobType = "image/jpeg";
            }
            const blob = new Blob([xhr.response], { type: blobType });

            const objectURL = window.URL.createObjectURL(blob);

            window.open(objectURL, url, "width=1100,height=768");

            window.URL.revokeObjectURL(objectURL);
          }
        };

        xhr.send();
        return false;
      }

      if (["doc", "docx", "xlsx", "xls"].includes(getFileFormatFun(url))) {
        const officeUrl = `https://view.officeapps.live.com/op/view.aspx?src=${url}`;
        window.open(officeUrl, url, "width=1100,height=768");
      } else {
        window.open(url, url, "width=800,height=700");
      }
    } else {
      console.error("缺少查看文件地址", url);
    }
  };

  onlineViewFile(file);
};

// 下载附件
const downloadAttachment = async (uri: string, fileName?: string) => {
  recordDownloadCount();

  // 使用fetch API获取文件
  const response = await fetch(uri);

  // 检查响应是否成功
  if (!response.ok) {
    throw new Error("网络响应错误");
  }

  // 获取Blob对象
  const blob = await response.blob();

  // 创建一个指向blob的URL
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  if (fileName) {
    // 如果提供了文件名，则设置下载的文件名
    link.download = fileName;
  }
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const checkPlugin = () => {
  window.open(
    `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${checkPluginData.value.guid}`,
  );
};

const sendCompete = () => {
  iframeUrl.value = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?MBRWGUID=${rrrr.value.guid}`;
  completeDialogVisible.value = true;
};

const editCompete = () => {
  iframeUrl.value = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?MBRWGUID=${rrrr.value.guid}`;
  completeDialogVisible.value = true;
};

const checkLastYear = () => {
  window.open(
    `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${rrrr.value.oldtaskid}`,
  );
};

const onAllBtnClick = async () => {
  onqbClick.value = true;
  formInline.yjzb = "";
  formInline.ejzb = "";
  formInline.sjzb = "";
  formInline.fzrxm = "";
  formInline.xdbm = "";
  formInline.ssbm = "";
  try {
    loading.value = true;
    const { data, count } = await getXYKPAllCollegeTargetTask(
      page.value,
      limit.value,
      "",
      "",
      route.query.code as string,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const exportExcel = () => {
  exportVisible.value = true;
};

// 导出确定
const exportSumbit = () => {
  const obj = {};
  checkList.value.forEach((item: any) => {
    obj[item] = true;
  });
  const url = `http://***********:7005/api/RWDC/ExportCollegeTargetTask?dczd=${JSON.stringify(
    [obj],
  )}&departmentName=${useDep.value}&ssmk=${formInline.ssmk}&ejzb=${
    formInline.ejzb
  }&sjzb=${formInline.sjzb}&xdbn=${formInline.xdbm}&fzrxm=${
    formInline.fzrxm
  }&ssbm=${formInline.ssbm}`;
  console.log(url);
  window.open(url);
  exportVisible.value = false;
};

// 提交评价
const submitEvaluate = async () => {
  const checkedGUID = multipleSelection.value
    .map((item) => item.guid)
    .join("|");
  const url = `https://ehall.zcmu.edu.cn:5003/iTask/Process210924165623?type=${multipleSelection.value[0].datatype}&xdbm=${multipleSelection.value[0].xdbm}&taskGUID=${checkedGUID}`;
  window.open(url);
  multipleTableRef.value.clearSelection();
};

const enterinfo = ref();
const onEnterInfo = () => {
  dialogEnterInfo.value = true;
  enterinfo.value = JSON.parse(JSON.stringify(currentBCSM.value));
};

const getRwmrfz = async () => {
  const res = await getRwmrfzr();
  pppp.value = res;
};

const nowdataData = ref<string>("");
const getnowdate = async () => {
  const res = await getNowDate();
  nowdataData.value = res;
};

const editSubmit = async () => {
  dialogEnterInfo.value = false;
  const { count } = await saveRWBCSM(rrrr.value.guid, enterinfo.value);
  if (count == 1) {
    ElMessage.success("录入补充说明成功！");
    currentBCSM.value = JSON.parse(JSON.stringify(enterinfo.value));
  }
};

const getMRFZR = async () => {
  const res = await getRwmrfzr();
  if (res === true) {
    declarationShow.value = true;
  } else {
    declarationShow.value = false;
  }
};

const currentEJZB = ref();
const getCurrentEjzb = async () => {
  formInline.fzrxm = "";
  const res = await getBBMEJZB("");
  currentEJZB.value = res.data;
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
};

const currentSsmk = ref();
const getcurrentSsmk = async () => {
  const res = await getBBMSSMK();
  currentSsmk.value = res.data;
};

const xdbmData = ref<GET_COLLEGEINFO_Type[]>([]);
const getAllDepe = async () => {
  const { data } = await getAllDep();
  xdbmData.value = data;
};

const bmxyData = ref();
const getbmxy = async () => {
  const { data } = await getBMXY();
  console.log(data);
  bmxyData.value = data;
};

const recordDownloadCount = async () => {
  updateXZCS(checkPluginData.value?.guid as string);
};

const handleChange = (value: string) => {
  if (value !== "2024") {
    tableData.value = [];
  } else {
    getCurrentYearRWByCollege();
  }
};

onMounted(async () => {
  await getRwmrfz();
  await getCurrentYearRWByCollege();
  getCollegeInfo();
  getCurrentUserDepname();
  getCurrentUserDep();
  getnowdate();
  getCurrentEjzb();
  getcurrentSsmk();
  getAllDepe();
  getMRFZR();
  getbmxy();
  getcurrentyjzb();
});
</script>

<style lang="less" scoped>
h2 {
  text-align: center;
}
.content {
  padding: 10px;
  p {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .content-item {
    color: rgb(148, 148, 148);
  }
}
.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 100px);
}
.left-header {
  text-align: center;
}
.header {
  height: 45px;
  .el-form {
    display: flex;
  }
}

.header-btn {
  text-align: center;
  margin-bottom: 5px;
}

.center {
  display: flex;
  justify-content: space-between;
  margin-right: 20px;
  .text {
    margin-top: 10px;
    span {
      color: rgb(148, 148, 148);
    }
  }
}
.tabledata {
  margin: 10px 20px 10px 0px;
}
.el-table {
  height: calc(100vh - 215px);
  border: 1px solid rgb(236, 238, 244);
}
.el-row {
  padding: 15px 20px 15px 20px;
}
:deep(.el-popper) {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}
.el-scrollbar {
  height: calc(100vh - 210px);
}
.center-btn {
  float: right;
  margin-right: 20px;
  margin-bottom: 10px;
}
:deep(.el-dialog__body) {
  padding: 0 20px 20px 20px;
}

.tag3 {
  background: #3896fb;
  color: #fff;
  margin-right: 5px;
}
</style>

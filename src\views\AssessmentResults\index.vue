<!--考核结果-->
<template>
  <el-row>
    <el-col :span="12">
      <h2>部门考核结果</h2>
      <div class="tabledata1">
        <el-table
          :data="tableData"
          :default-sort="{ prop: 'date', order: 'descending' }"
          style="width: 100%"
        >
          <template #empty>
            <div class="no-data">
              <span>返回的数据不符合规范，正确的成功状态码应为："code": 0</span>
            </div>
          </template>
          <el-table-column prop="date" label="部门" width="150" />
          <el-table-column prop="name" label="基础性指标" sortable />
          <el-table-column prop="address" label="获A率" sortable />
          <el-table-column prop="address" label="A贡献率" sortable />
          <el-table-column prop="address" label="考核得分" sortable />
        </el-table>
      </div>
    </el-col>
    <el-col :span="12">
      <h2>学院考核结果</h2>
      <div class="tabledata2">
        <el-table
          :data="tableData2"
          :default-sort="{ prop: 'date', order: 'descending' }"
          style="width: 100%"
        >
          <el-table-column prop="date" label="部门" width="150" />
          <el-table-column prop="name" label="基础性指标" sortable />
          <el-table-column prop="address" label="获A率" sortable />
          <el-table-column prop="address" label="A贡献率" sortable />
          <el-table-column prop="address" label="考核得分" sortable />
        </el-table>
      </div>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
interface User {
  date: string;
  name: string;
  address: string;
}
const tableData: User[] = [];
const tableData2 = [
  {
    date: "2016-05-03",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles",
  },
];
</script>

<style lang="less" scoped>
h2 {
  margin: 0;
  padding: 0;
  margin-left: 290px;
}
.tabledata1 {
  border: 1px solid #f5f5f5;
  margin: 15px 15px 10px 0;

  .el-table {
    position: relative;
    height: calc(100vh - 140px);
  }
  .no-data {
    position: absolute;
    width: 500px;
    height: 50px;
    // background-color: pink;
    left: 90px;
    top: 0;
  }
}
.tabledata2 {
  margin: 15px 0px 10px 10px;
  border: 1px solid #f5f5f5;
  height: calc(100vh - 150px);
  /deep/.el-table__inner-wrapper {
    // position: relative;
    height: calc(100vh - 140px);
  }
}
.el-row {
  padding: 20px;
}
</style>

<template>
  <el-row style="padding: 0">
    <el-col :span="19">
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
          @row-click="handleRowClick"
          :row-key="(row: any) => row.guid"
          highlight-current-row
          v-loading="loading"
          :cell-style="columnStyle"
        >
          <el-table-column width="50px" align="center" fixed>
            <template #default="{ row }">
              <el-radio v-model="radio" :label="row.guid"></el-radio>
            </template>
          </el-table-column>
          <el-table-column
            prop="sjzb"
            label="指标等级"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag v-if="row.zrlx == '系统集成'" style="border: none">
                系统抽
              </el-tag>
              <span>{{ row.zbrw }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="mbyq"
            label="考核内容及要求"
            width="200"
            sortable
            show-overflow-tooltip
          />
          <el-table-column
            prop="ssbm"
            label="责任部门"
            width="90"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ssbm"
            label="完成学院"
            width="100"
            align="center"
            show-overflow-tooltip
            v-if="cccc == null"
          />
          <el-table-column
            prop="xyzp"
            label="学院自评"
            width="120"
            sortable
            align="center"
            show-overflow-tooltip
            :formatter="driverNameFormat"
          ></el-table-column>
          <el-table-column
            prop="wcqk"
            label="完成情况（单击编辑）"
            width="300"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <span v-if="row.zrlx == '系统集成'">
                本部门已完成:{{ row.countState }}项
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-col>
    <el-col :span="5">
      <div class="left-header">
        <h2>目标完成情况</h2>
        <span>{{ currentClickRw }}</span>
      </div>
      <el-divider style="margin: 10px 0" />
      <el-scrollbar>
        <div v-if="currentBCSM !== ''">
          <h3 style="text-align: center; margin-bottom: 5px">
            本部门已完成：{{ currentCount }}项
          </h3>
          <span
            style="
              display: block;
              text-align: center;
              font-size: 12px;
              color: rgb(148, 148, 148);
            "
          >
            同步时间：{{ nowdataData }} 02:00:00
          </span>
          <div style="text-align: center">
            <router-link
              target="_blank"
              :to="{
                path: '/WCQK',
                query: { guid: rowInfo.guid, depName: rowInfo.ssbm },
              }"
              style="padding: 10px; color: #2a59b6; cursor: pointer"
            >
              查看完成情况
            </router-link>
          </div>
          <div class="bottom-plugin">
            <el-divider style="margin: 10px 0" />
            <h2>补充说明情况</h2>
            <el-divider style="margin: 10px 0" />
            <span>{{ delHtmlTag(currentBCSM) }}</span>
          </div>
        </div>
        <div class="connent" v-else>
          <span v-if="isShow">修订时间：{{ currentWCS }}</span>
          <br />
          <span v-if="isShow">自评结果：{{ cuttentzpjg }}</span>
          <p>{{ currentWCQK }}</p>
          <el-button
            type="primary"
            size="small"
            style="margin-top: 10px"
            @click="checkPlugin"
            v-if="checkPluginData !== undefined"
          >
            查看详情
          </el-button>
          <div class="fujian" v-if="isShow">
            <h3 v-if="currentFJ !== null">附件：</h3>
            <div class="fujian-plugin">
              <a
                @click="onFy(item.uri)"
                style="cursor: pointer"
                v-for="(item, index) of currentFJ"
                :key="index"
              >
                {{ item.name }}
              </a>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import {
  getCOLLEGEDETAILByTaskId,
  getWCQKByRWId,
  getBCSMById,
  getCurrentUserDepName,
  getHQWCQK,
  getNowDate,
} from "@/api/TYJK/index";
import { onMounted, ref } from "vue";
import axios from "axios";
import { useAuth } from "vue-oidc-provider";

const { user } = useAuth();
const columnStyle = ({ columnIndex, row }: any) => {
  if (row.xyzp !== null && columnIndex == 5) {
    return {
      color: "green",
    };
  }
  if (row.xyzp == null && columnIndex == 5) {
    return {
      color: "red",
    };
  }
};

const driverNameFormat = (row: any) => {
  return row.xyzp == null ? "未评价" : row.xyzp;
};

// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
const cuttentzpjg = ref();
const currentParams = ref();
const currentCount = ref();
const checkPluginData = ref();
const currentClickRw = ref<string>("");
const currentWCS = ref<string>("");
const currentWCQK = ref<string | null>("");
const currentFJ = ref({
  item: {
    name: "",
    uri: "",
  },
});
const currentBCSM = ref<string | null>("");
const isShow = ref<boolean>(false);
const props = defineProps(["currentClick"]);
const radio = ref<boolean>(false);
const loading = ref<boolean>(false);
const rowInfo = ref();
const tableData = ref<GET_RWFJZT_Type[]>([]);
const cccc = ref();

const countState = ref();
// 获取学校目标情况分解任务
const getCollegeCompletionDetail = async () => {
  try {
    loading.value = true;
    const { data } = await getCOLLEGEDETAILByTaskId(props.currentClick.guid);
    console.log(data);
    // 查看完成情况
    for await (const item of data) {
      const res1 = await getHQWCQK(item.guid, item.ssbm);
      if (res1.code == 200) {
        currentParams.value = res1.data;
        const res2 = await axios.get(
          `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
          {
            headers: {
              Authorization: `Bearer ${user.value?.access_token}`,
            },
          },
        );
        if (res2.data.count !== 0) {
          item.countState = res2.data.count;
        } else {
          item.countState = 0;
        }
      } else {
        item.countState = 0;
      }
    }
    cccc.value = data[0].sfbmtxxyjck;
    tableData.value = data;
  } finally {
    loading.value = false;
  }
};

// 单击每一行任务
const handleRowClick = async (row: any) => {
  radio.value = row.guid;
  rowInfo.value = row;
  currentClickRw.value = row.zbrw;
  if (row.zrlx == "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
  } else {
    const { data } = await getWCQKByRWId(row.guid);
    checkPluginData.value = data[0];
    if (checkPluginData.value !== undefined) {
      currentWCS.value = data[0].wcsj.slice(0, 10);
      currentWCQK.value = data[0].wcqk;
      cuttentzpjg.value = data[0].zpjg;
      currentFJ.value = JSON.parse(data[0].fj);
      isShow.value = true;
    }
  }
};

const checkPlugin = () => {
  const url = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${checkPluginData.value.guid}`;
  window.open(url);
};

const onFy = (uri: string) => {
  const url = `https://ydmh.guangshaxy.com/formWebSite/form/${uri}`;
  window.open(url);
};

const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const nowdataData = ref<string>("");
const getnowdate = async () => {
  const res = await getNowDate();
  nowdataData.value = res;
};

onMounted(() => {
  getCollegeCompletionDetail();
  getCurrentUserDep();
  getnowdate();
});
</script>

<style lang="less" scoped>
.el-table {
  height: 500px;
  border: 1px solid rgb(236, 238, 244);
}
/deep/.el-row {
  padding: 0;
}
.left-header {
  text-align: center;
  h3 {
    color: #7a7a7a;
    font-weight: 400;
  }
}
.bottom-plugin {
  padding: 10px;
  margin-top: 200px;
}
h2 {
  text-align: center;
}
h4 {
  padding: 10px;
  color: #2a59b6;
}
.connent {
  padding: 10px;
  p {
    margin-top: 10px;
  }
}
.el-col-5 {
  border: 1px solid rgb(236, 238, 244);
}
.el-scrollbar {
  height: 390px;
}
.table {
  margin-right: 10px;
}
.el-tag {
  background: #5a9cf8;
  color: #fff;
  margin-right: 5px;
}
.fujian {
  margin-top: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 150px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}
</style>

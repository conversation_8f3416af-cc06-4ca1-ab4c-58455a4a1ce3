<script setup lang="ts">
import CollegeComparisonChart from "./components/CollegeComparisonChart.vue";
import TrendAnalysisChart from "./components/TrendAnalysisChart.vue";
import GrowthPrediction from "./components/GrowthPrediction.vue";
import WarningIndicatorAnalysis from "./components/WarningIndicatorAnalysis.vue";
</script>

<template>
  <div class="statistics-warning">
    <div class="statistics-warning-modules">
      <div class="title">学院对比分析</div>
      <CollegeComparisonChart />
    </div>
    <div class="statistics-warning-modules">
      <div class="title">趋势变化分析</div>
      <TrendAnalysisChart />
    </div>
    <div class="statistics-warning-modules">
      <div class="title">发展规律预测</div>
      <GrowthPrediction />
    </div>
    <div class="statistics-warning-modules">
      <div class="title">预警指标分析</div>
      <WarningIndicatorAnalysis />
    </div>
  </div>
</template>

<style lang="less">
.statistics-warning {
  padding: 20px 10px 0px 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  height: calc(100vh - 60px);
  overflow: auto;

  .statistics-warning-modules {
    width: calc(50% - 20px);
    margin: 0 10px 20px 10px;
    border: 1px solid rgb(232, 232, 232);
    border-radius: 10px;
    overflow: auto;

    .title {
      height: 40px;
      text-align: center;
      font-size: 26px;
      font-weight: bold;
      margin: 10px 0 0 0;
    }
  }
}
</style>

interface GET_ZNBM_ZBLX_Type {
  id: string;
  name: string;
  datatype: string;
  px: number;
  children?: any[];
}

interface GET_ZRBM_BYND_Type {
  px: number;
  nd: string;
  bmid: string;
  name: string;
  ssbm: string;
  zblx: string;
  children?: {
    name: string;
    bmid: string;
    datatype: string;
    nd: string;
    px: number;
    ssbm: string;
    children?: any[] | undefined;
  };
}
interface GET_ALLZBLX_Type {
  guid: srtring;
  lxmc: string;
  px: number;
}

interface GET_AllZBLY_Type {
  guid: string;
  lymc: string;
}

interface GET_AllZRBM_Type {
  guid: string;
  departmentid: string;
  departmentname: string;
  datatype: string;
  px: number;
}

interface Result<T> {
  code: number;
  count: number;
  data: T;
  msg: string;
}

interface ADDRW_Type {
  guid: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  bz: string;
  bmssmbzrlx: string;
  sjrw: string;
  wcqk: string;
  ck: string;
  zrlx: string;
  ssbm: string;
  zbly: string;
  nd: string;
  dqzt: string;
  hxxzb: string;
  sflhzb: string;
  createtime: string;
  ssmk: string;
  sfbmtxxyjck: string;
  xbbm: string;
}

interface SEARCH_YEAR_Type {
  year: string;
  page: number;
  limit: number;
  zbrw?: string;
  ssbm?: string;
}

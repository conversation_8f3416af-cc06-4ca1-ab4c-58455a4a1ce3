<script setup lang="ts">
import { AuthProvider, AuthProviderProps } from "vue-oidc-provider";
import { presetOidcSettings } from "./oidc/presetOidcSettings";

const oidcSettings: AuthProviderProps = {
  ...presetOidcSettings,
  ...window.projectConfig.oidcSettings,
  redirect_uri: window.location.origin,
  onSigninCallback: (user) => {
    window.history.replaceState({}, document.title, window.location.pathname);
  },
  acr_values: "idp:Platform",
};
</script>

<template>
  <div id="app">
    <AuthProvider :options="oidcSettings">
      <router-view />
    </AuthProvider>
  </div>
</template>

<style lang="less" scoped>
@import "@/assets/styles/tableColour";
</style>

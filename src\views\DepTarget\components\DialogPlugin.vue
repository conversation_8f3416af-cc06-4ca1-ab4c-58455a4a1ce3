<template>
  <el-form ref="formRef" :model="form" label-width="150px" :rules="rules">
    <el-form-item label="一级指标" prop="yjzb">
      <el-select v-model="form.yjzb" filterable placeholder="直接选择或者搜索选择" style="width: 100%" @change="getYjzb">
        <el-option v-for="item in currentYjzb" :key="item.guid" :label="item.mc" :value="item.guid"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="二级指标" prop="ejzb">
      <el-select v-model="form.ejzb" filterable placeholder="直接选择或者搜索选择" style="width: 100%" @change="getEjzb">
        <el-option v-for="item in currentEJZB" :key="item.guid" :value="item.guid" :label="item.mc"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="三级指标" prop="sjzb">
      <el-select v-model="form.sjzb" filterable placeholder="直接选择或者搜索选择" style="width: 100%" @change="getzblx">
        <el-option v-for="item in currentSJZB" :key="item.guid" :label="item.mc" :value="item.guid"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="考核内容及标准" prop="zbrw">
      <el-input v-model="form.zbrw" placeholder="请输入目标任务" type="textarea" />
    </el-form-item>
    <el-form-item label="指标解释" prop="mbyq">
      <el-input v-model="form.mbyq" type="textarea" placeholder="请输入目标要求" />
    </el-form-item>
    <el-form-item label="指标类型" prop="zblx">
      <el-select v-model="form.zblx" filterable placeholder="直接选择或者搜索选择" style="width: 100%" @change="getzblx">
        <el-option v-for="item in zblxdata" :key="item.guid" :label="item.lxmc" :value="item.lxmc"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="责任部门" prop="ssbm">
      <el-select v-model="form.ssbm" filterable placeholder="直接选择或者搜索选择" style="width: 100%"
        :multiple="!props.form.guid">
        <el-option v-for="item in ssbmdata" :key="item.departmentid" :label="item.departmentname"
          :value="item.departmentid" />
      </el-select>
    </el-form-item>
    <el-form-item label="协办部门" prop="xbbm">
      <el-select v-model="form.xbbm" multiple filterable style="width: 700px" clearable :disabled="disabled">
        <el-option v-for="item of xbbmdata" :label="item.departmentname" :value="item.departmentid"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="备注" prop="bz">
      <el-input v-model="form.bz" type="textarea" placeholder="请输入备注" />
    </el-form-item>
    <el-form-item label="目标来源类型" prop="zrlx">
      <el-radio-group v-model="form.zrlx">
        <el-radio label="自主填报">自主填报</el-radio>
        <el-radio label="系统集成">系统集成</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="是否为量化指标" prop="sflhzb">
      <el-radio-group v-model="form.sflhzb">
        <el-radio label="是">是</el-radio>
        <el-radio label="否">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="该指标类型量化规则" prop="lhzbgz" v-if="form.sflhzb === '是'">
      <template #default="{ row }">
        <el-table ref="multipleTableRef" :data="zbgzTabledata" style="width: 100%" border v-model="form.lhzbgz">
          <el-table-column v-for="(item, index) in lhzbArr" :key="index" :label="item" align="center" min-width="200"
            property="selectValue">
            <template #default="{ row }">
              <div class="parent">
                <el-select v-model="row['selectValue' + index]" placeholder="请选择" class="son1">
                  <el-option v-for="item in options" :label="item" :value="item" />
                </el-select>
                <el-input v-model="row['mb' + index]" class="son2" />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-form-item>
    <el-form-item label="是否部门核心指标" prop="hxxzb">
      <el-radio-group v-model="form.hxxzb">
        <el-radio label="是">是</el-radio>
        <el-radio label="否">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="是否由部门填写，学院仅查看" prop="sfbmtxxyjck">
      <el-radio-group v-model="form.sfbmtxxyjck">
        <el-radio label="是">是</el-radio>
        <el-radio label="否">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">保存</el-button>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { getALLZBLX, getALLZBLY, getALLZRBM } from "@/api/MBXD/index";
import {
  editRW,
  addRW,
  getRWById,
  getZBLX,
  getBMYJZB,
  getBBMEJZB,
  getBBMSJZB,
} from "@/api/TYJK/index";
import { FormInstance, ElMessage } from "element-plus";
import { ref, onMounted, watch } from "vue";
const zbgzTabledata = ref<GET_RWFJZT_Type[]>([]);
const form = ref<GET_ADDUPDATERW_Type>({
  yjzb: "",
  ejzb: "",
  zblx: "",
  guid: "",
  zbrw: "",
  mbyq: "",
  bz: "",
  bmssmbzrlx: "",
  sjrw: "",
  wcqk: "",
  ck: "",
  zrlx: "自主填报",
  ssbm: [],
  zbly: [],
  nd: "",
  sflhzb: "否",
  dqzt: "",
  hxxzb: "否",
  ssmk: "",
  sfbmtxxyjck: "否",
  xbbm: [],
  hosttaskid: "",
  sjzb: "",
});
const lhzbArr = ref([]);
const disabled = ref<boolean>(false);
const formRef = ref<FormInstance>();
const props = defineProps(["form", "onDialogVisibleSubmit"]);
const emits = defineEmits(["onDialogVisibleSubmit"]);
// 字段校验
const rules = {
  zbrw: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  mbyq: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  zblx: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  bmssmbzrlx: [
    { required: true, message: "请输入不可为空喔", trigger: "blur" },
  ],
  zbly: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  ssbm: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  zrlx: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  sflhzb: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  hxxzb: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  sfbmtxxyjck: [
    { required: true, message: "请输入不可为空喔", trigger: "blur" },
  ],
};

const options = ["大于", "大于等于", "小于"];
const zblxdata = ref<GET_ALLZBLX_Type[]>([]);
const zblydata = ref<GET_AllZBLY_Type[]>([]);
const xbbmdata = ref<GET_AllZRBM_Type[]>([]);
const ssbmdata = ref<GET_AllZRBM_Type[]>([]);
// 获取指标类型
const getAllzblx = async () => {
  const { data } = await getALLZBLX();
  zblxdata.value = data;
};
// 获取指标来源
const getAllzbly = async () => {
  const { data } = await getALLZBLY();
  zblydata.value = data;
};
// 获取责任部门
const getAllssbm = async () => {
  const { data } = await getALLZRBM();
  ssbmdata.value = data;
};
// 获取协办部门
const getAllxbbm = async () => {
  const { data } = await getALLZRBM();
  xbbmdata.value = data;
};

// 监听 props.form 的变化
watch(() => props.form, (newVal) => {
  // 重置表单字段
  formRef.value?.resetFields();

  if (newVal && Object.keys(newVal).length > 0 && newVal.guid) {
    // 编辑模式：使用传入的值
    form.value = { ...newVal };
  } else {
    // 添加模式：使用默认值
    form.value = {
      yjzb: "",
      ejzb: "",
      zblx: "",
      guid: "",
      zbrw: "",
      mbyq: "",
      bz: "",
      bmssmbzrlx: "",
      sjrw: "",
      wcqk: "",
      ck: "",
      zrlx: "自主填报",
      ssbm: [], 
      zbly: [],
      nd: "",
      sflhzb: "否",
      dqzt: "",
      hxxzb: "否",
      ssmk: "",
      sfbmtxxyjck: "否",
      xbbm: [],
      hosttaskid: "",
      sjzb: "",
    };
  }
}, { immediate: true });

// 数据初始化
const init = async () => {
  console.log(props.form);

  if (props.form.guid) {
    const { data } = await getRWById(props.form.guid);
    const obj = data[0];

    form.value = { ...obj };

    if (obj.zbly !== null) {
      form.value.zbly = JSON.parse(obj.zbly).map((item: any) => item.name);
    }
    if (obj.xbbm !== null) {
      form.value.xbbm = JSON.parse(obj.xbbm).map((item: any) => item.name);
    }
    // 编辑时不需要转换为数组
    // if (obj.ssbm !== null) {
    //   form.value.ssbm = [obj.ssbm];
    // }
    // 是否是量化指标
    if (obj.sflhzb == null) {
      form.value.sflhzb = "否";
    }
    // 考核规则
    if (obj.lhzbgz) {
      let temp = {};
      let lhzbgz = (form.value.lhzbgz = JSON.parse(obj.lhzbgz));
      for (let i = 0; i < lhzbgz.length; i++) {
        temp["selectValue" + i] = lhzbgz[i].gz;
        temp["mb" + i] = lhzbgz[i].mb;
      }
      zbgzTabledata.value.push(temp);
    }

    form.value.hosttaskid !== null
      ? (disabled.value = true)
      : (disabled.value = false);
  } else {

  }
};

const getrwByid = async () => {
  const { data } = await getRWById(form.value.guid as string);
  if (data.length > 0) {
    form.value.bmssmbzrlx = data[0].bmssmbzrlx;
  }
};

// 保存
const submit = async () => {
  const valid = await formRef.value?.validate();
  if (valid) {
    if (props.form.guid) {
      form.value.lhzbgz = "";
      // 处理指标来源数据
      const editTemp = zblydata.value
        .map((item: any, index: any) => {
          const isValue = form.value.zbly.indexOf(item.guid);
          if (isValue > -1) {
            return {
              name: zblydata.value[index].lymc,
              value: zblydata.value[index].guid,
              selected: true,
            };
          }
        })
        .filter((value) => {
          return value !== undefined;
        });
      form.value.zbly = JSON.stringify(editTemp);
      // 处理协办部门数据
      if (form.value.xbbm !== null) {
        const editObj = xbbmdata.value
          .map((item: any, index: any) => {
            const isObj = form.value.xbbm.indexOf(item.departmentid);
            if (isObj > -1) {
              return {
                name: xbbmdata.value[index].departmentname,
                value: xbbmdata.value[index].departmentid,
                selected: true,
              };
            }
          })
          .filter((value) => {
            return value !== undefined;
          });
        form.value.xbbm = JSON.stringify(editObj);
      }

      // 编辑时直接保存
      const { code } = await editRW(form.value);
      if (code === 0) {
        ElMessage.success("编辑成功");
        emits("onDialogVisibleSubmit");
      }
    } else {
      // 添加
      form.value.lhzbgz = "";
      // 处理指标来源数据
      const addTemp = zblydata.value
        .map((item: any, index: any) => {
          const isValue = form.value.zbly.indexOf(item.guid);
          if (isValue > -1) {
            return {
              name: zblydata.value[index].lymc,
              value: zblydata.value[index].guid,
              selected: true,
            };
          }
        })
        .filter((value) => {
          return value !== undefined;
        });
      form.value.zbly = JSON.stringify(addTemp);
      // 处理协办部门数据
      const addObj = xbbmdata.value
        .map((item: any, index: any) => {
          const isObj = form.value.xbbm.indexOf(item.departmentid);
          if (isObj > -1) {
            return {
              name: xbbmdata.value[index].departmentname,
              value: xbbmdata.value[index].departmentid,
              selected: true,
            };
          }
        })
        .filter((value) => {
          return value !== undefined;
        });

      form.value.xbbm = JSON.stringify(addObj);

      // 新增时遍历责任部门数组，分别保存
      const ssbmArray = [...form.value.ssbm];
      for (const ssbm of ssbmArray) {
        const formData = { ...form.value, ssbm };
        const { code } = await addRW(formData);
        if (code !== 0) {
          ElMessage.error(`部门${ssbm}保存失败`);
          return;
        }
      }
      ElMessage.success("添加成功");
      emits("onDialogVisibleSubmit");
      form.value = {};
    }
  }
};

// 获取动态表格表头
const getzblx = async () => {
  const res = await getZBLX(form.value.zblx as string);
  lhzbArr.value = res.data;
  if (!form.value.guid) {
    let temp = [{}];
    zbgzTabledata.value = temp;
    if (!lhzbArr.value || !lhzbArr.value.length) return;
    zbgzTabledata.value.map((ele) => {
      for (let i = 0; i < lhzbArr.value.length; i++) {
        let name = "selectValue" + i;
        ele[name] = "大于等于";
      }
      return ele;
    });
  }
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
  console.log(currentYjzb.value);
};

const currentEJZB = ref();
const getYjzb = async () => {
  const res = await getBBMEJZB(form.value.yjzb);
  currentEJZB.value = res.data;
};

const currentSJZB = ref();
const getEjzb = async () => {
  const { data } = await getBBMSJZB(form.value.ejzb as string);
  currentSJZB.value = data;
};

watch(
  () => form.value.yjzb,
  async () => {
    console.log("一级指标变化", form.value.yjzb);

    if (props.form.guid) {
      await getYjzb();
      await getEjzb();
    }
  },
  { deep: true }
);

onMounted(async () => {
  await init();
  getAllxbbm();
  getAllzbly();
  getAllzblx();
  getAllssbm();
  getrwByid();
  getzblx();
  getcurrentyjzb();
});
</script>

<style lang="less" scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}

.el-form {
  .el-select {
    .el-input__inner {
      width: 500px;
    }
  }
}

/deep/.el-dialog__body {
  padding: 10px 20px 5px 0;
}

.parent {
  display: flex;

  .son2 {
    margin-left: 10px;
  }
}

.el-table {
  .el-select {
    /deep/.el-input__inner {
      min-width: 70px;
    }
  }
}
</style>

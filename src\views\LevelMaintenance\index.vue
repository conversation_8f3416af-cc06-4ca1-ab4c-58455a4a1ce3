<!-- 指标维护 -->
<template>
  <div class="main-page">
    <div class="connent-main">
      <div class="head-add">
        <el-button @click="addOrg()" type="primary" size="small">
          + 添加指标
        </el-button>
        <el-button type="danger" size="small" @click="lotDelete">
          - 批量删除
        </el-button>
        <el-button type="warning" size="small" :icon="CopyDocument" @click="openCopyDialog">复制</el-button>
        <!--         <el-button type="success" size="small" @click="exportData">导出</el-button>-->
        <!--         <el-button type="danger" size="small" @click="importData">导入</el-button>-->
        <!--         <el-button size="small" type="warning" @click="setManange"> 设置管理员 </el-button>-->
      </div>
      <div class="search">
        <el-input v-model="inputFirstValue" placeholder="请输入内容" :prefix-icon="Search" />
      </div>
      <el-tree style="max-width: 600px" :data="treeData" :props="props" node-key="guid" :expand-on-click-node="false"
        show-checkbox highlight-current :height="208" check-strictly  
        @node-expand="handleNodeClick" @check="
          (click: any, checked: any, index: any) => {
            handleCheckChange(click, checked, index);
          }
        ">
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <div class="name" :title="data.mc" @click="handleNodeClick(data)">{{ data.mc }}</div>
            <div>
              <el-button type="primary" link @click="appendStatus(data)">
                {{ data.sfqy === "是" ? "禁用" : "启用" }}
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
    <div class="connent-right">
      <el-scrollbar>
        <router-view @eventHandler="eventHandler"></router-view>
      </el-scrollbar>
    </div>
  </div>
  <el-drawer v-model="drawer" :direction="direction" :before-close="handleDrawerClose"
    @update:modelValue="handleDrawerClose">
    <template #header>
      <h4 style="font-weight: 300; margin: 0">添加指标</h4>
    </template>
    <template #default>
      <el-form ref="ruleFormRef" style="max-width: 600px" :model="ruleForm" :rules="rules" label-width="auto"
        class="demo-ruleForm" :size="formSize" status-icon>
        <el-form-item label="指标名称" prop="departmentName">
          <el-input v-model="ruleForm.mc" maxlength="50" show-word-limit />
        </el-form-item>
        <!-- <el-form-item label="排序" prop="orderNo">
          <el-input v-model.number="ruleForm.orderNo" type="text" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="remark" type="textarea" />
        </el-form-item> -->
        <el-form-item style="margin-left: 80px">
          <el-button type="primary" small @click="saveFirstOrg">保存</el-button>
        </el-form-item>
      </el-form>
    </template>
  </el-drawer>
  <el-dialog v-model="dialogVisible" title="设置管理员" width="500" :before-close="handleClose">
    <el-form ref="setFormRef" style="max-width: 600px" :model="form" :rules="setrule" label-width="auto"
      class="demo-ruleForm" :size="formSize" status-icon>
      <el-form-item label="管理员" prop="manage">
        <el-input v-model="form.manage" class="inline-input" style="max-width: 600px">
          <template #append>
            <!-- <el-button @click="openSelect">选择</el-button> -->
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="saveSetManager">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="importVisible" title="数据导入" width="600" top="30vh">
    <div class="upload-box">
      <el-button type="primary" @click="handleDawnLoad">模版下载</el-button>
      <el-upload class="upload-demo" action="" accept=".xls,.xlsx" :show-file-list="false" :http-request="httpRequest">
        <el-button type="success">点击上传</el-button>
      </el-upload>
    </div>
  </el-dialog>
  <el-dialog v-model="copyFormVisible" title="复制" width="500">
    <div style="display: flex; align-items: center;">
      <span style="flex-shrink: 0;">指标名称：</span>
      <el-input required v-model="copyMc" maxlength="50" show-word-limit />
    </div>
    <template #footer>
      <div class="copy-footer">
        <el-button @click="copyFormVisible = false">取消</el-button>
        <el-button type="primary" @click="copyHandle">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { addZBJB, getZBJB, getZBJBXX, Delete_ZBJB, copyZBJB, updateQyZBJB } from "../../api/TYJK/index";
import type { Get_ZBJB_type, Result } from "../../api/TYJK/type";
import { Search } from "@element-plus/icons-vue";
import { log } from "console";
import {
  ElInput,
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
  type UploadRequestOptions,
} from "element-plus";
import { CopyDocument } from "@element-plus/icons-vue";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";

const importVisible = ref(false);
const router = useRouter();
const ruleFormRef = ref<FormInstance>();
const dialogVisible = ref(false);
const remark = ref("");
const drawer = ref(false);
const direction = ref("rtl");
const inputFirstValue = ref("");
const treeData = ref<Get_ZBJB_type[]>([]);
const copyFormVisible = ref(false);
const copyMc = ref<string>("");
const props = {
  value: "guid",
  label: "mc",
  children: "children",
};
interface RuleForm {
  mc: string;
}
const formSize = ref("default");

const ruleForm = reactive<RuleForm>({
  mc: "",
});
const rules = reactive<FormRules<RuleForm>>({
  mc: [{ required: true, message: "名称不可为空", trigger: "blur" }],
});

interface Form {
  manage: string;
}

const setFormRef = ref<FormInstance>();
const form = reactive<Form>({
  manage: "",
});
const setrule = reactive<FormRules<Form>>({
  manage: [
    { required: true, message: "超级管理员名称不可为空", trigger: "blur" },
  ],
});

const saveSetManager = async () => { };

const handleClose = () => {
  dialogVisible.value = false;
  form.manage = "";
  setFormRef.value?.resetFields();
};

const handleDrawerClose = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields();
  }
  drawer.value = false;
};

const selectArray = ref([]);
const handleCheckChange = (click: any, checked: any, index: any) => {
  selectArray.value = checked.checkedNodes;
};

const addOrg = () => {
  drawer.value = true;
};

const saveFirstOrg = async () => {
  const valid = await ruleFormRef.value?.validate();
  if (valid) {
    await addZBJB(ruleForm.mc, "");
    drawer.value = false;
    ElMessage.success("添加成功");
    getTreeData();
  }
};

const eventHandler = async () => {
  const res = await getZBJB();
  if (res.data) {
    treeData.value = res.data;
    console.log(treeData.value);
  }
};

const getTreeData = async () => {
  const res = await getZBJB();
  if (res.data) {
    treeData.value = res.data;
    console.log(treeData.value, "treeData");

  }
};

const lotDelete = () => {
  ElMessageBox.confirm("你所删除的指标可能存在下级，是否确定删除？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const ids = selectArray.value.map((item: any) => item.guid);
    // 多次删除
    await Promise.all(
      ids.map(async (id) => {
        await Delete_ZBJB(id);
      }),
    );
    ElMessage.success("删除成功");
    getTreeData();
  });
};

const handleNodeClick = async (nodeData: any) => {
  console.log(nodeData);
  const res = await getZBJBXX(nodeData.guid);
  if (res.data) {
    nodeData.children = res.data[0].xjzb;
    for (const item of nodeData.children) {
      await mapHandleNode(item);
    }
  }
  router.push({
    name: "LevelMaintenanceId",
    params: { id: nodeData.guid },
  });
};

const mapHandleNode = async (node: any) => {
  if (node) {
    const res = await getZBJBXX(node.guid);
    if (res.data) {
      node.children = res.data[0].xjzb;
    }
  }
};

const handleDawnLoad = () => {
  window.open(
    "http://fangke.org-stru.edu.cn:8008/form/attachments/files/************/部门导入模板.xlsx",
  );
  importVisible.value = false;
};

const httpRequest = async (options: UploadRequestOptions) => { };

const actionPicker = () => {
  importVisible.value = true;
};
const openCopyDialog = async () => {
  console.log(selectArray.value, "selectArray");
  if (selectArray.value.length === 0) {
    ElMessage.warning("请选择要复制的指标");
    return;
  }
  if (selectArray.value.length > 1) {
    ElMessage.warning("请选择一项指标进行复制");
    return;
  }
  copyFormVisible.value = true;


  copyFormVisible.value = true;
};
const copyHandle = async () => {
  if (copyMc.value.trim() === "") {
    ElMessage.warning("指标名称不能为空");
    return;
  }
  const selectedItem = selectArray.value[0];
  console.log(selectedItem, "selectedItem");

  try {
    await copyZBJB(selectedItem.guid, copyMc.value);
    ElMessage.success("复制成功");
    copyFormVisible.value = false;
    getTreeData();
  } catch (error) {
    ElMessage.error("复制失败，请稍后再试");
  }
};

// 递归更新节点及其所有子级的状态
const updateNodeAndChildren = (node: Get_ZBJB_type, newStatus: string) => {
  node.sfqy = newStatus;

  // 更新 children 中的所有子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      updateNodeAndChildren(child, newStatus);
    });
  }

  // 更新 xjzb 中的所有子节点
  if (node.xjzb && node.xjzb.length > 0) {
    node.xjzb.forEach(child => {
      updateNodeAndChildren(child, newStatus);
    });
  }
};

// 递归查找并更新树节点的状态（包括其所有子级）
const updateTreeNodeStatus = (nodes: Get_ZBJB_type[], targetGuid: string, newStatus: string): boolean => {
  for (let node of nodes) {
    if (node.guid === targetGuid) {
      // 找到目标节点，更新它及其所有子级
      updateNodeAndChildren(node, newStatus);
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (updateTreeNodeStatus(node.children, targetGuid, newStatus)) {
        return true;
      }
    }
    if (node.xjzb && node.xjzb.length > 0) {
      if (updateTreeNodeStatus(node.xjzb, targetGuid, newStatus)) {
        return true;
      }
    }
  }
  return false;
};

const appendStatus = async (item: Get_ZBJB_type) => {
  console.log(item.guid, "item");
  let sfqy = item.sfqy == "是" ? "否" : "是";

  try {
    const res = await updateQyZBJB(item.guid, sfqy);
    console.log(res, "res");

    if (res) {
      // 手动更新源数据中对应项的状态，而不是重新获取整个树
      updateTreeNodeStatus(treeData.value, item.guid, sfqy);
      ElMessage.success("操作成功");
    }
  } catch (error) {
    console.error("更新状态失败:", error);
    ElMessage.error("操作失败，请稍后再试");
  }
}

onMounted(() => {
  getTreeData();
});
</script>

<style lang="less" scoped>
.main-page {
  display: flex;

  .connent-main {
    width: 350px;
    height: calc(100vh - 60px);
    border-left: 1px solid #dedede;
    border-right: 1px solid #dedede;

    .head-add {
      padding-top: 15px;
      text-align: center;
    }

    .search {
      margin: 0 15px;
      margin-bottom: 20px;
      margin-top: 10px;
    }
  }

  .connent-right {
    width: calc(100% - 300px);
    height: calc(100vh - 60px);
  }

  .el-scrollbar {
    height: calc(100vh - 60px);
  }
}

.upload-box {
  display: flex;

  .upload-demo {
    margin-left: 20px;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  box-sizing: border-box;

  .name {
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
<style lang="less">
.el-tree-node__content {
  height: 40px;
  line-height: 40px;
  font-size: 14px;

  .el-tree-node__expand-icon {
    font-size: 16px;
  }
}
</style>
@/pages/org-stru/api/index@/pages/org-stru/api/type@/pages/org-stru/oc-selector

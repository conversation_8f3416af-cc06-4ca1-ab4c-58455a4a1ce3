<template>
  <div class="home">
    <!-- <div class="header">
      <div class="header-title">突破性指标</div>
      <div class="header-connent">
        <encouragePlugin></encouragePlugin>
      </div>
    </div> -->
    <div class="center">
      <div class="left">
        <div class="left-foot">
          <div class="title">
            学院指标
            <span
              style="
                font-size: 8px;
                color: RGB(148, 148, 148);
                margin-left: 5px;
              "
            >
              (已完成指标数/总指标数)
            </span>
          </div>

          <el-scrollbar height="225px">
            <div class="foot-connent">
              <div
                class="connent-item"
                v-for="(item, index) in ejxyInfo"
                :key="index"
                @click="getBMMC(item)"
              >
                <span></span>
                <!--                <a href="#">-->
                <a>
                  <div class="biaoqian">
                    <div>{{ item.bmmc }}</div>
                    ：
                    <div>
                      <em style="color: blue">{{ item.zbwcs }}</em>
                      /{{ item.zbzs }}
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </el-scrollbar>
        </div>

        <div class="left-foot" style="margin-top: 10px">
          <div class="title">
            党支部指标
            <span
              style="
                font-size: 8px;
                color: RGB(148, 148, 148);
                margin-left: 5px;
              "
            >
              (已完成指标数/总指标数)
            </span>
          </div>
          <el-scrollbar height="225px">
            <div class="foot-connent">
              <div
                class="connent-item"
                v-for="(item, index) in dzzInfo"
                :key="index"
                @click="getBMMC(item)"
              >
                <span></span>
                <!--                <a href="#">-->
                <a>
                  <div class="biaoqian">
                    <div>{{ item.bmmc }}</div>
                    ：
                    <div>
                      <em style="color: blue">{{ item.zbwcs }}</em>
                      /{{ item.zbzs }}
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>

      <div class="right">
        <div class="title">
          部门指标
          <span
            style="font-size: 8px; color: RGB(148, 148, 148); margin-left: 5px"
          >
            (已完成指标数/总指标数)
          </span>
        </div>

        <el-scrollbar max-height="500px">
          <div class="foot-connent">
            <div
              class="connent-item"
              v-for="(item, index) in znbmInfo"
              :key="index"
              @click="getBMMC(item)"
            >
              <span></span>
              <!--              <a href="#">-->
              <a>
                <div class="biaoqian">
                  <div>{{ item.bmmc }}</div>
                  ：
                  <div>
                    <em style="color: blue">{{ item.zbwcs }}</em>
                    /{{ item.zbzs }}
                  </div>
                </div>
              </a>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getDZZWCQK, getWJXYWCQK, getZNBMWCQK } from "@/api/Home";

import encouragePlugin from "./components/encouragePlugin.vue";
import { ref, onBeforeMount, reactive } from "vue";
import router from "@/router";

const dzzInfo = reactive<GET_WCQK_TYPE[]>([]);
const znbmInfo = reactive<GET_WCQK_TYPE[]>([]);
const ejxyInfo = reactive<GET_WCQK_TYPE[]>([]);

// 获取党总支信息
const getDZZInfo = async () => {
  let res = await getDZZWCQK();
  for (let i = 0; i < res.data.length; i++) {
    let arr = {
      bmmc: res.data[i].bmmc,
      zbwcs: res.data[i].zbwcs,
      zbzs: res.data[i].zbzs,
    };
    dzzInfo.push(arr);
  }
};

// 获取职能部门信息
const getZNBMInfo = async () => {
  let res = await getZNBMWCQK();
  for (let i = 0; i < res.data.length; i++) {
    let arr = {
      bmmc: res.data[i].bmmc,
      zbwcs: res.data[i].zbwcs,
      zbzs: res.data[i].zbzs,
    };
    znbmInfo.push(arr);
  }
};

// 获取二级学院信息
const getEJXYInfo = async () => {
  let res = await getWJXYWCQK();
  for (let i = 0; i < res.data.length; i++) {
    let arr = {
      bmmc: res.data[i].bmmc,
      zbwcs: res.data[i].zbwcs,
      zbzs: res.data[i].zbzs,
    };
    ejxyInfo.push(arr);
  }
};

const getBMMC = (item: GET_WCQK_TYPE) => {
  router.push({
    name: "DeptartmentTargetTaskReportAll",
    query: { ssbm: item.bmmc },
  });
};

onBeforeMount(async () => {
  await getDZZInfo();
  await getZNBMInfo();
  await getEJXYInfo();
});
</script>

<style lang="less">
.home {
  width: 100%;
  min-height: 100vh - 6px;
  background-color: #f0f2f3;
  padding: 5px 20px 15px 20px;
  .header {
    width: 100%;
    margin: 10px 0;
    padding: 10px 15px 2px 15px;
    background-color: #fff;
    border-radius: 15px;
    .header-title {
      font-size: 18px;
      font-weight: bold;
    }
    .header-year {
      position: fixed;
      width: 413px;
      height: 20px;
      border-top: solid rgb(242 244 245) 25px;
      top: 74px;
      right: 22px;
      transform: skewX(30deg);
      border-radius: 5px;
    }
    .sjx_div {
      float: right;
      position: fixed;
      top: 75px;
      right: 47px;
      background-color: rgb(242 244 245);
      margin-top: 2px;
      .skew {
        display: inline-block;
        width: 60px;
        height: 20px;
        border-radius: 5px;
        color: #bfbfbf;
        background-color: #fafafa;
        margin: 0 auto;
        font-size: 14px;
        text-align: center;
        font-weight: bold;
        margin: 0 2.2px;
        cursor: pointer;
        transform: skew(30deg);
        line-height: 20px;
        p {
          display: block;
          transform: skew(-30deg);
        }
      }
    }
  }
  .center {
    display: flex;
    .left {
      width: 59%;
      margin-right: 5px;
      .left-header {
        width: 100%;
        height: 160px;
        min-height: 300px;
        background-color: #fff;
        border-radius: 15px;
        padding: 10px 15px 2px 15px;
      }
      .left-foot {
        width: 100%;
        height: 275px;
        background-color: #fff;
        border-radius: 15px;
        .title {
          font-size: 18px;
          font-weight: bold;
          padding: 10px 15px 2px 15px;
        }
        .foot-connent {
          display: flex;
          padding: 0 15px 10px 10px;
          flex-wrap: wrap;
          .connent-item {
            position: relative;
            width: 24%;
            background-color: pink;
            background: #f5f7fb;
            border: 1px solid #dee5f3;
            border-radius: 4px;
            margin: 3px;
            span {
              display: block;
              position: absolute;
              width: 8px;
              height: 8px;
              background: #1a66f9;
              border-radius: 50%;
              top: 35%;
              left: 8px;
            }
            a {
              display: flex;
              display: block;
              height: 35px;
              line-height: 35px;
              &:hover {
                color: #1a66f9;
              }
              .biaoqian {
                width: 100%;
                display: flex;
                margin-left: 23px;
                font-size: 12px;
                div {
                  max-width: 50%;
                  overflow: hidden; //块元素超出隐藏
                  text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
                  white-space: nowrap; //规定段落中的文本不进行换行
                }
              }
            }
          }
        }
      }
    }
    .right {
      width: 41%;
      height: 560px;
      background-color: #fff;
      margin-left: 5px;
      border-radius: 15px;
      .title {
        font-size: 18px;
        font-weight: bold;
        padding: 10px 15px 2px 15px;
      }
      .foot-connent {
        display: flex;
        padding: 0 15px 10px 10px;
        flex-wrap: wrap;
        .connent-item {
          position: relative;
          width: 49%;
          height: 35px;
          background: #f5f7fb;
          border: 1px solid #dee5f3;
          border-radius: 4px;
          margin: 3px;
          span {
            display: block;
            position: absolute;
            width: 8px;
            height: 8px;
            background: #1a66f9;
            border-radius: 50%;
            top: 35%;
            left: 8px;
          }
          a {
            display: flex;
            display: block;
            height: 35px;
            line-height: 35px;
            &:hover {
              color: #1a66f9;
            }
            .biaoqian {
              width: 100%;
              display: flex;
              margin-left: 23px;
              font-size: 12px;
              div {
                max-width: 50%;
                overflow: hidden; //块元素超出隐藏
                text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
                white-space: nowrap; //规定段落中的文本不进行换行
              }
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="department-sub">
    <div class="header">
      <el-icon><Share /></el-icon>
      <h3>下级指标</h3>
      <!-- <span>(总数量：{{ depCount }}个)</span> -->
    </div>
    <div class="connent">
      <div class="connent-heade">
        <el-button size="small" @click="addChild">添加子指标</el-button>
      </div>
      <el-scrollbar>
        <div
          class="connent-table"
          v-for="item in orgInfoData"
          :key="item.guid"
          @click="onclick(item.guid)"
          style="cursor: pointer"
        >
          <div class="table-left">
            <el-icon><OfficeBuilding /></el-icon>
            <span>{{ item.mc }}</span>
          </div>
          <el-icon style="margin-right: 8px"><ArrowRight /></el-icon>
        </div>
      </el-scrollbar>
    </div>
  </div>
  <el-drawer
    v-model="drawer"
    :direction="direction"
    :before-close="handleDrawerClose"
    @update:modelValue="handleDrawerClose"
  >
    <template #header>
      <h4 style="font-weight: 300; margin: 0">添加子指标</h4>
    </template>
    <template #default>
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="指标名称" prop="mc">
          <el-input v-model="ruleForm.mc" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item style="margin-left: 80px">
          <el-button type="primary" small @click="saveOrgInfo">保存</el-button>
        </el-form-item>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { addZBJB, getZBJBXX } from "../../../api/TYJK/index";
import type { Get_ZBJB_type, Result } from "../../../api/TYJK/type";
import { ArrowRight, OfficeBuilding, Share } from "@element-plus/icons-vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { onMounted, reactive, ref, watch } from "vue";
import { useAuth } from "vue-oidc-provider";
import { useRoute, useRouter } from "vue-router";

// const { user } = useAuth()
const router = useRouter();
const route = useRoute();
const props = defineProps(["deptId"]);

const drawer = ref(false);
const direction = ref("rtl");
interface RuleForm {
  mc: string;
}
const orgInfoData = ref<Get_ZBJB_type[]>([]);
const formSize = ref("default");
const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
  mc: "",
});
const rules = reactive<FormRules<RuleForm>>({
  mc: [
    { required: true, message: "名称不可为空", trigger: "blur" },
    { min: 1, max: 50, message: "Length should be 1 to 50", trigger: "blur" },
  ],
});

const addChild = () => {
  drawer.value = true;
};

const saveOrgInfo = async () => {
  const valid = await ruleFormRef.value?.validate();
  if (valid) {
    await addZBJB(ruleForm.mc, props.deptId);
    drawer.value = false;
    ElMessage.success("添加成功");
    getorginfpmation();
  }
};

const handleDrawerClose = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields();
  }
  drawer.value = false;
};

const depCount = ref(0);
const getorginfpmation = async () => {
  const { data, count } = await getZBJBXX(props.deptId);
  depCount.value = count;
  orgInfoData.value = data[0].xjzb;
};

const onclick = (id: string) => {
  router.push({
    params: { id },
    query: {
      parentId: props.deptId,
    },
  });
};
const selectData = ref<Array<any>>([]);

const depPerUser = ref();
const getCurrentPermission = async () => {};

const userPer = ref();
const getCurPermission = async () => {};

watch(
  () => props.deptId,
  (newValue, oldValue) => {
    if (newValue) {
      getorginfpmation();
      getCurrentPermission();
      getCurPermission();
    }
  },
  { immediate: true },
);
</script>

<style lang="less" scoped>
.department-sub {
  // min-height: 300px;
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    h3 {
      font-size: 20px;
      font-weight: 400;
      margin: 0;
      margin-left: 8px;
      margin-right: 8px;
    }
    .el-icon {
      font-size: 25px;
    }
  }
  .connent {
    .connent-heade {
      height: 44px;
      background-color: #e6e6e9;
      line-height: 40px;
      padding-left: 10px;
      .el-button {
        padding: 13px 20px;
      }
    }
    .connent-table {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 47px;
      line-height: 47px;
      border-bottom: 1px solid #e6e6e9;
      .table-left {
        display: flex;
        align-items: center;
        .el-icon {
          font-size: 14px;
          margin-right: 10px;
        }
        span {
          font-size: 14px;
          font-weight: 300;
        }
      }
    }
  }
}
.el-scrollbar {
  height: calc(100vh - 60px);
}
</style>

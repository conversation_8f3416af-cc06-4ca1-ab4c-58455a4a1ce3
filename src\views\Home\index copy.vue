<template>
  <div class="home">
    <div class="header">
      <div class="header-title">突破性指标</div>
      <div class="header-year"></div>
      <div class="sjx_div">
        <div class="skew">
          <p style="transform: skew(-30deg); color: #1a66f9">2022</p>
        </div>
        <div class="skew">
          <p style="transform: skew(-30deg)">2021</p>
        </div>
        <div class="skew">
          <p style="transform: skew(-30deg)">...</p>
        </div>

        <div class="skew">
          <p style="transform: skew(-30deg)">...</p>
        </div>
        <div class="skew">
          <p style="transform: skew(-30deg)">...</p>
        </div>
        <div class="skew">
          <p style="transform: skew(-30deg)">...</p>
        </div>
      </div>
      <div class="header-connent">
        <encouragePlugin></encouragePlugin>
      </div>
    </div>
    <div class="center">
      <div class="left">
        <div class="left-header">
          <div class="title">
            <div class="title-left">核心监测指标</div>
            <div class="search">
              <el-input v-model="inputValue" placeholder="请输入目标任务" />
              <el-button type="primary">搜索</el-button>
            </div>
          </div>
          <div class="main-table">
            <div class="table-th">
              <div>目标任务</div>
              <div>责任部门</div>
              <div>完成情况</div>
              <div>评价</div>
            </div>
            <!-- <div class="table-td">
              <a href="#">
                <span></span>
                <div style="padding-left: 12px">
                  本科学生发明专利本科学生发明专利本科学生发明专利
                </div>
                <div>研究生院（研究生工作部、学位办研究生工作部）</div>
                <div>未上报</div>
                <div>未完成</div>
              </a>
            </div>
            <div class="table-td">
              <a href="#">
                <span></span>
                <div style="padding-left: 12px">本科学生发明专利</div>
                <div>研究生院（研究生工作部、学位办）</div>
                <div>未上报</div>
                <div>未完成</div>
              </a>
            </div>
            <div class="table-td">
              <a href="#">
                <span></span>
                <div style="padding-left: 12px">本科学生发明专利</div>
                <div>研究生院（研究生工作部、学位办）</div>
                <div>未上报</div>
                <div>未完成</div>
              </a>
            </div> -->
          </div>
        </div>
        <div class="left-foot">
          <div class="title">
            学院指标
            <span
              style="
                font-size: 8px;
                color: RGB(148, 148, 148);
                margin-left: 5px;
              "
            >
              (已填报指标数/总指标数)
            </span>
          </div>
          <!-- <el-scrollbar height="120px">
            <div class="foot-connent">
              <div class="connent-item">
                <span></span>
                <a href="#">
                  <div class="biaoqian">
                    <div>基础医学院</div>
                    ：
                    <div><em style="color: blue">1111</em>/1111</div>
                  </div>
                </a>
              </div>

              <div class="connent-item">
                <span></span>
                <a href="#">
                  <div class="biaoqian">
                    <div>第一临床医学院</div>
                    ：
                    <div><em style="color: blue">1111</em>/1111</div>
                  </div>
                </a>
              </div>
              <div class="connent-item">
                <span></span>
                <a href="#">
                  <div class="biaoqian">
                    <div>第一临床医学院临床医学</div>
                    ：
                    <div><em style="color: blue">1111</em>/1111</div>
                  </div>
                </a>
              </div>
              <div class="connent-item">
                <span></span>
                <a href="#">
                  <div class="biaoqian">
                    <div>第一临床医学院临床医学</div>
                    ：
                    <div><em style="color: blue">1111</em>/1111</div>
                  </div>
                </a>
              </div>
            </div>
          </el-scrollbar> -->
        </div>
      </div>
      <div class="right">
        <div class="title">
          部门指标
          <span
            style="font-size: 8px; color: RGB(148, 148, 148); margin-left: 5px"
          >
            (已填报指标数/总指标数)
          </span>
        </div>
        <!-- <el-scrollbar max-height="570px">
          <div class="foot-connent">
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>

            <div class="connent-item">
              <span></span>
              <a href="#">
                <div class="biaoqian">
                  <div>第一临床医学院临床医学</div>
                  ：
                  <div><em style="color: blue">1111</em>/1111</div>
                </div>
              </a>
            </div>
          </div>
        </el-scrollbar> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import encouragePlugin from "./components/encouragePlugin.vue";
import { ref } from "vue";
const inputValue = ref("");
</script>

<style lang="less">
.home {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f3;
  padding: 5px 20px 15px 20px;
  .header {
    width: 100%;
    margin: 10px 0;
    padding: 10px 15px 2px 15px;
    background-color: #fff;
    border-radius: 15px;
    .header-title {
      font-size: 18px;
      font-weight: bold;
    }
    .header-year {
      position: fixed;
      width: 413px;
      height: 20px;
      border-top: solid rgb(242 244 245) 25px;
      top: 74px;
      right: 22px;
      transform: skewX(30deg);
      border-radius: 5px;
    }
    .sjx_div {
      float: right;
      position: fixed;
      top: 75px;
      right: 47px;
      background-color: rgb(242 244 245);
      margin-top: 2px;
      .skew {
        display: inline-block;
        width: 60px;
        height: 20px;
        border-radius: 5px;
        color: #bfbfbf;
        background-color: #fafafa;
        margin: 0 auto;
        font-size: 14px;
        text-align: center;
        font-weight: bold;
        margin: 0 2.2px;
        cursor: pointer;
        transform: skew(30deg);
        line-height: 20px;
        p {
          display: block;
          transform: skew(-30deg);
        }
      }
    }
  }
  .center {
    display: flex;
    .left {
      width: 58%;
      margin-right: 5px;
      .left-header {
        width: 100%;
        height: 440px;
        min-height: 300px;
        background-color: #fff;
        border-radius: 15px;
        padding: 10px 15px 2px 15px;
        .title {
          display: flex;
          justify-content: space-between;
          .title-left {
            font-size: 18px;
            font-weight: bold;
          }
          .search {
            display: flex;
          }
        }
        .main-table {
          margin-top: 5px;
          .table-th {
            display: flex;
            text-align: center;
            justify-content: center;
            width: 100%;
            height: 45px;
            line-height: 45px;
            background: #f5f7fb;
            border: 1px solid #dee5f3;
            border-radius: 4px;
            margin-bottom: 5px;
            div {
              width: 25%;
              font-size: 15px;
              font-weight: bold;
            }
          }
          .table-td {
            position: relative;
            width: 100%;
            background: #f5f7fb;
            border: 1px solid #dee5f3;
            border-radius: 4px;
            margin: 10px 0;
            padding: 10px;
            a {
              display: flex;
              justify-content: center;
              text-align: center;
              &:hover {
                color: #1a66f9;
              }
              span {
                display: block;
                position: absolute;
                width: 8px;
                height: 8px;
                background: #1a66f9;
                border-radius: 50%;
                top: 40%;
                left: 8px;
              }
              div {
                width: 25%;
                padding: 0 5px;
                font-size: 12px;
              }
            }
          }
        }
      }
      .left-foot {
        width: 100%;
        height: 170px;
        background-color: #fff;
        border-radius: 15px;
        margin-top: 10px;
        .title {
          font-size: 18px;
          font-weight: bold;
          padding: 10px 15px 2px 15px;
        }
        .foot-connent {
          display: flex;
          padding: 0 15px 10px 10px;
          flex-wrap: wrap;
          .connent-item {
            position: relative;
            width: 24%;
            background-color: pink;
            background: #f5f7fb;
            border: 1px solid #dee5f3;
            border-radius: 4px;
            margin: 3px;
            span {
              display: block;
              position: absolute;
              width: 8px;
              height: 8px;
              background: #1a66f9;
              border-radius: 50%;
              top: 35%;
              left: 8px;
            }
            a {
              display: flex;
              display: block;
              height: 35px;
              line-height: 35px;
              &:hover {
                color: #1a66f9;
              }
              .biaoqian {
                width: 100%;
                display: flex;
                margin-left: 23px;
                font-size: 12px;
                div {
                  max-width: 50%;
                  overflow: hidden; //块元素超出隐藏
                  text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
                  white-space: nowrap; //规定段落中的文本不进行换行
                }
              }
            }
          }
        }
      }
    }
    .right {
      width: 42%;
      height: 620px;
      min-height: 479px;
      background-color: #fff;
      margin-left: 5px;
      border-radius: 15px;
      .title {
        font-size: 18px;
        font-weight: bold;
        padding: 10px 15px 2px 15px;
      }
      .foot-connent {
        display: flex;
        padding: 0 15px 10px 10px;
        flex-wrap: wrap;
        .connent-item {
          position: relative;
          width: 32%;
          height: 35px;
          background: #f5f7fb;
          border: 1px solid #dee5f3;
          border-radius: 4px;
          margin: 3px;
          span {
            display: block;
            position: absolute;
            width: 8px;
            height: 8px;
            background: #1a66f9;
            border-radius: 50%;
            top: 35%;
            left: 8px;
          }
          a {
            display: flex;
            display: block;
            height: 35px;
            line-height: 35px;
            &:hover {
              color: #1a66f9;
            }
            .biaoqian {
              width: 100%;
              display: flex;
              margin-left: 23px;
              font-size: 12px;
              div {
                max-width: 50%;
                overflow: hidden; //块元素超出隐藏
                text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
                white-space: nowrap; //规定段落中的文本不进行换行
              }
            }
          }
        }
      }
    }
  }
}
</style>

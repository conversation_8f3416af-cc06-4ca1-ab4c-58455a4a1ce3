<!--年终考核-->
<template>
  <div v-if="reload">
    <el-row>
      <el-col :span="4">
        <h2>目标任务</h2>
        <el-divider style="margin: 10px 0" />
        <div class="scroll-container">
          <div class="scroll-content" v-scroll>
            <el-tree
              :data="treeData"
              :props="defaultProps"
              @node-click="handleNodeClick"
              highlight-current
            />
          </div>
        </div>
      </el-col>
      <el-col :span="16">
        <div class="header">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <!-- <el-form-item label="一级指标">
              <el-select
                v-model="formInline.yjzb"
                placeholder="请选择"
                clearable
                @change="(val:string) => changeYjzb(val)"
              >
                <el-option
                  v-for="item in currentYjzb"
                  :key="item.guid"
                  :label="item.yjzb"
                  :value="item.yjzb"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="二级指标">
              <el-select
                v-model="formInline.ejzb"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in currentEJZB"
                  :key="item.guid"
                  :value="item.ejzb"
                  :label="item.ejzb"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item label="年度">
              <el-select v-model="year" @change="handleChange">
                <el-option label="2025" value="2025"></el-option>
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2023" value="2023"></el-option>
                <el-option label="2022" value="2022"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="指标等级">
              <el-input
                v-model="formInline.sjzb"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="考核内容及标准">
              <el-input
                v-model="formInline.zbrw"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="下达部门">
              <el-select
                v-model="formInline.xdbm"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in xdbmData"
                  :key="item.guid"
                  :label="item.bmmc"
                  :value="item.bmmc"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchData">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button @click="getAllRW">全部</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="center">
          <div class="btn">
            <!-- <el-button type="primary" @click="handleReport">提交评价</el-button> -->
            <el-button @click="exportExcel">导出</el-button>
          </div>
        </div>
        <div class="tabledata">
          <el-table
            :data="tableData"
            style="width: 100%"
            v-loading="loading"
            @row-click="handleRowClick"
            :row-key="(row: any) => row.guid"
            highlight-current-row
          >
            <el-table-column width="50px" align="center" fixed>
              <template #default="{ row }">
                <el-radio v-model="radio" :label="row.guid"></el-radio>
              </template>
            </el-table-column>

            <el-table-column
              prop="bmgxypj"
              label="最终评价"
              align="center"
              show-overflow-tooltip
              width="90"
            >
              <!-- <template #default="{ row }">
                <el-input
                  v-model="row.bmgxypj"
                  placeholder="请输入数字"
                  @change="(val:number) => onBmgxypjInput(val, row)"
                />
              </template> -->
            </el-table-column>
            <el-table-column
              prop="xyzp"
              label="自评分数"
              align="center"
              show-overflow-tooltip
              width="90"
            ></el-table-column>
            <!-- <el-table-column
              prop="sjzbfz"
              label="分值"
              align="center"
              show-overflow-tooltip
              width="90"
            >
            </el-table-column> -->
            <!-- <el-table-column
              property="yjzb"
              label="一级指标"
              sortable
              show-overflow-tooltip
              min-width="120px"
            >
            </el-table-column>
            <el-table-column
              property="ejzb"
              label="二级指标"
              sortable
              show-overflow-tooltip
              min-width="150px"
            >
            </el-table-column> -->
            <el-table-column
              prop="sjzb"
              label="指标等级"
              sortable
              min-width="150px"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <el-tag
                  v-if="row.hosttaskid !== null"
                  style="border: none"
                  size="small"
                >
                  协办
                </el-tag>
                <el-tag
                  v-if="row.zrlx == '系统集成'"
                  style="border: none"
                  size="small"
                >
                  系统抽
                </el-tag>
                <span>{{ row.zbrw }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="ssbm"
              label="责任部门"
              sortable
              width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="mbyq"
              label="考核内容及标准"
              sortable
              min-width="150px"
              show-overflow-tooltip
            />

            <!--            <el-table-column-->
            <!--              prop="ssmk"-->
            <!--              label="所属模块"-->
            <!--              sortable-->
            <!--              align="center"-->
            <!--              show-overflow-tooltip-->
            <!--              width="130"-->
            <!--            />-->
            <el-table-column
              prop="xdbm"
              label="下达部门"
              sortable
              width="120"
              show-overflow-tooltip
            />
          </el-table>
        </div>
        <div class="right-page">
          <!-- 底部分页 -->
          <el-pagination
            v-model:current-page="page"
            v-model:page-size="limit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="total"
            class="page-fen"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
      <el-col :span="4">
        <div class="left-header">
          <h2>目标完成情况</h2>
        </div>

        <el-divider style="margin: 10px 0" />
        <el-scrollbar>
          <div v-if="currentBCSM !== ''">
            <h3 style="text-align: center; margin-bottom: 5px">
              本部门已完成：{{ currentCount }}项
            </h3>
            <div style="text-align: center">
              <router-link
                target="_blank"
                :to="{
                  path: '/WCQK',
                  query: { guid: rowInfo.guid, depName: curdep },
                }"
                style="padding: 10px; color: #2a59b6; cursor: pointer"
              >
                查看完成情况
              </router-link>
            </div>
            <div class="bottom-plugin">
              <el-divider style="margin: 10px 0" />
              <h2>补充说明情况</h2>
              <el-divider style="margin: 10px 0" />
              <span>{{ delHtmlTag(currentBCSM) }}</span>
            </div>
          </div>
          <div class="aaaa" v-else>
            <div class="header-btn" style="margin-left: 10px">
              <!-- <el-button
                type="primary"
                size="small"
                class="header-btn"
                @click="sendCompete"
                id="sendCompeteid"
                v-if="sbwcqkBtnShow"
              >
                上报完成情况
              </el-button> -->
            </div>
            <div class="connent" v-for="item in currentRWData" :key="item.guid">
              <span>修订时间：{{ item.sqsj }}</span>
              <br />
              <span>完成情况：{{ item.wcqk }}</span>
              <!-- <p>{{ item.wcqk }}</p> -->
              <!-- <el-button
                size="small"
                type="primary"
                style="margin-top: 10px"
                @click="checkPlugin(item.guid)"
                v-if="item.wcqk !== null"
                >查看详情
              </el-button> -->
              <div class="fujian" v-if="fjshow">
                <h3>附件：</h3>
                <div class="fujian-plugin">
                  <a
                    @click="onFy(item.url)"
                    style="cursor: pointer"
                    v-for="(item, index) of item.fj"
                    :key="index"
                  >
                    {{ item.name }}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </el-col>
    </el-row>
    <el-dialog
      v-model="dialogFormVisible"
      title="各学院完成情况"
      width="1500"
      hight="300px"
      destroy-on-close
    >
      <DialogPlugin :currentClickId="currentClickId" />
    </el-dialog>
    <el-dialog
      v-model="exportVisible"
      title="选择导出字段"
      width="500px"
      hight="300px"
      destroy-on-close
    >
      <el-checkbox-group v-model="checkList">
        <el-checkbox
          v-for="(item, index) in exportZDList"
          :key="index"
          :label="item.value"
          size="large"
        >
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="exportSumbit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import {
  getRwkallnd,
  getRwzblxBynd,
  getAllZNBMRW,
  getWCQKByRWId,
  getRWFJZTById,
  getBCSMById,
  getAllFunDepTargetTask,
  saveBmYearEndEvaluate,
  getCurrentUserDepName,
  getHQWCQK,
  getBBMEJZB,
  getBBMSSMK,
  saveWCQK,
  getBMYJZB,
  getFunctionalDepartmenttask,
  getAllBM,
} from "@/api/TYJK/index";
import { getBMND } from "@/api/BMKH/index";
import { getALLZBLX } from "@/api/MBXD/index";
import { reactive, onMounted, ref, nextTick } from "vue";
import { getCurYear } from "@/utils";
import { ElMessage, ElMessageBox } from "element-plus";
// import DialogPlugin from "./components/DialogPlugin.vue";
import { useAuth } from "vue-oidc-provider";
import axios from "axios";

const { user } = useAuth();

const year = ref<string>("2024");

// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
const sbwcqkBtnShow = ref<boolean>(false); //上报完成情况
const checkList = ref([
  "nzpj",
  "xyzp",
  "zbrw",
  "mbyq",
  "zbly",
  "zblx",
  "ssmk",
  "ssbm",
  "xbbm",
  "wcqk",
  "fj",
]);
const exportZDList = [
  {
    label: "学校评价",
    value: "nzpj",
  },
  {
    label: "部门自评",
    value: "xyzp",
  },
  {
    label: "目标任务",
    value: "zbrw",
  },
  {
    label: "目标要求",
    value: "mbyq",
  },
  {
    label: "指标来源",
    value: "zbly",
  },
  {
    label: "指标类型",
    value: "zblx",
  },
  {
    label: "所属模块",
    value: "ssmk",
  },
  {
    label: "责任部门",
    value: "ssbm",
  },
  {
    label: "协办部门",
    value: "xbbm",
  },
  {
    label: "完成情况",
    value: "wcqk",
  },
  {
    label: "有无附件",
    value: "fj",
  },
];
const fjshow = ref(false);
const exportVisible = ref<boolean>(false);
const dialogFormVisible = ref<boolean>(false);
const radio = ref<boolean>(false);
const rowInfo = ref();
const currentYear = ref<number>(0);
const loading = ref<boolean>(false);
const zblxdata = ref<GET_ALLZBLX_Type[]>([]);
const ssbm = ref("");
const nd = ref("");
const zblx = ref("");
const reload = ref(true);
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const znbm = ref<string>("职能部门");
const currentClickId = ref<string>("");
const currentRWData = ref<GET_BCSM_Type[]>([]);
const currentBCSM = ref<string | null>("");
const formInline = reactive({
  yjzb: "",
  ejzb: "",
  sjzb: "",
  xdbm: "",
  zbrw: "",
});
const tableData = ref<GET_ALLZNBMRW_Type[]>([]);
interface DD {
  name: string;
  bmid: string;
  datatype: string;
  nd: string;
  px: number;
  ssbm: string;
  children?: any[] | undefined;
}

interface Tree {
  label: string;
  ssbm: string;
  children?: Tree[] | undefined;
}
const defaultProps = {
  children: "children",
  label: "label",
};
const selectSsbm = ref();
const treeData: Tree[] = [
  {
    label: "部门",
    ssbm: "",
    children: [
      {
        label: "办公室",
        ssbm: "办公室",
      },
      {
        label: "组织部",
        ssbm: "组织部",
      },
      {
        label: "党委宣传部",
        ssbm: "党委宣传部",
      },
      {
        label: "人事处",
        ssbm: "人事处",
      },
      {
        label: "纪检室、监察室、审计室",
        ssbm: "纪检室、监察室、审计室",
      },
      {
        label: "教务处",
        ssbm: "教务处",
      },
      {
        label: "招生就业处",
        ssbm: "招生就业处",
      },
      {
        label: "发展规划处",
        ssbm: "发展规划处",
      },
      {
        label: "科研与社会合作处（学报编辑部）",
        ssbm: "科研与社会合作处（学报编辑部）",
      },
      {
        label: "财务处",
        ssbm: "财务处",
      },
      {
        label: "实验室建设与设备管理处",
        ssbm: "实验室建设与设备管理处",
      },
      {
        label: "后勤处",
        ssbm: "后勤处",
      },
      {
        label: "保卫处",
        ssbm: "保卫处",
      },
      {
        label: "数字信息中心",
        ssbm: "数字信息中心",
      },
      {
        label: "图书馆",
        ssbm: "图书馆",
      },
      {
        label: "国际交流处",
        ssbm: "国际交流处",
      },
      {
        label: "质量控制与评估办公室",
        ssbm: "质量控制与评估办公室",
      },
      {
        label: "创业学院",
        ssbm: "创业学院",
      },
      {
        label: "国际学院",
        ssbm: "国际学院",
      },
      {
        label: "党校办",
        ssbm: "党校办",
      },
      {
        label: "档案室",
        ssbm: "档案室",
      },
      {
        label: "党委组织部、党委统战部",
        ssbm: "党委组织部、党委统战部",
      },

      {
        label: "教师发展中心",
        ssbm: "教师发展中心",
      },
      {
        label: "工会、妇联",
        ssbm: "工会、妇联",
      },
      {
        label: "学工部、学生处",
        ssbm: "学工部、学生处",
      },
      {
        label: "校友办",
        ssbm: "校友办",
      },
      {
        label: "高教所",
        ssbm: "高教所",
      },
      {
        label: "科研与社会合作处",
        ssbm: "科研与社会合作处",
      },
      {
        label: "学报编辑部",
        ssbm: "学报编辑部",
      },
      {
        label: "学工部、学生处、团委",
        ssbm: "学工部、学生处、团委",
      },
      {
        label: "宣传部",
        ssbm: "宣传部",
      },
      {
        label: "招就处",
        ssbm: "招就处",
      },
    ],
  },
  {
    label: "学院",
    ssbm: "",
    children: [
      {
        label: "建筑工程学院",
        ssbm: "建筑工程学院",
      },
      {
        label: "管理工程学院",
        ssbm: "管理工程学院",
      },
      {
        label: "国际商学院",
        ssbm: "国际商学院",
      },
      {
        label: "艺术设计学院",
        ssbm: "艺术设计学院",
      },
      {
        label: "信息学院",
        ssbm: "信息学院",
      },
      {
        label: "智能制造学院",
        ssbm: "智能制造学院",
      },
    ],
  },
  {
    label: "教学部",
    ssbm: "",
    children: [
      {
        label: "人文学院",
        ssbm: "人文学院",
      },
      {
        label: "体育学院",
        ssbm: "体育学院",
      },
      {
        label: "马克思主义学院",
        ssbm: "马克思主义学院",
      },
    ],
  },
];
const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const searchData = async () => {
  try {
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      zbrw: formInline.zbrw,
      xdbm: formInline.xdbm,
      ssbm: selectSsbm.value ? selectSsbm.value.ssbm : "",
    };
    loading.value = true;
    const res = await getFunctionalDepartmenttask(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
      selectSsbm.value ? selectSsbm.value.nd : "",
    );
    total.value = res.count;
    tableData.value = res.data;
  } finally {
    loading.value = false;
  }
};

const handleReport = () => {
  const url = `https://ehall.zcmu.edu.cn:5003/iTask/Process210825144828?type=${znbm.value}`;
  window.open(url);
};

// 获取左侧树形数据
const handleNodeClick = async (data: Tree, nodeValue: any) => {
  selectSsbm.value = data;
  if (data.ssbm) {
    ssbm.value = data.ssbm;
    page.value = 1;
    getclickndrw();
    return;
  }
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  getclickndrw();
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  getclickndrw();
};

const changeYjzb = async (val: string) => {
  const res = await getBBMEJZB(val);
  currentEJZB.value = res.data;
};

// 获取到全部的任务
const getAllRW = async () => {
  formInline.yjzb = "";
  formInline.ejzb = "";
  formInline.sjzb = "";
  formInline.zbrw = "";
  formInline.xdbm = "";
  ssbm.value = "";
  try {
    loading.value = true;
    const { data, count } = await getFunctionalDepartmenttask(
      (page.value = 1),
      (limit.value = 15),
      "",
      currentYear.value,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const onBmgxypjInput = async (val: any, row: any) => {
  const res = await saveWCQK([row]);
  if (res.code === 200 && res.count === 1) {
    ElMessage.success("编辑成功");
  }
};

// 根据筛选渲染表格数据
const getclickndrw = async () => {
  try {
    loading.value = true;
    const { data, count } = await getFunctionalDepartmenttask(
      page.value,
      limit.value,
      JSON.stringify({ ...formInline, ssbm: ssbm.value }),
      nd.value,
    );
    // for await (const item of data) {
    //   item.dddd = await getXYWCQKHtml(item.guid, item.hosttaskid);
    // }
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const getcurrentallbmrw = async () => {
  try {
    loading.value = true;
    const { data, count } = await getFunctionalDepartmenttask(
      page.value,
      limit.value,
      "",
      currentYear.value,
    );
    total.value = count;
    tableData.value = data;
  } finally {
    loading.value = false;
  }
};

const getAllzblx = async () => {
  const { data } = await getALLZBLX();
  zblxdata.value = data;
};

const currentParams = ref();
const currentCount = ref();
// 点击表格某一行获取目标任务
const handleRowClick = async (row: any) => {
  radio.value = row.guid;
  rowInfo.value = row;
  currentBCSM.value = "";
  currentRWData.value = [];
  if (row.zrlx == "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
  } else {
    const { data } = await getWCQKByRWId(row.guid);
    // 自主填报
    if (row.zrlx == "自主填报" && row.hosttaskid == null) {
      if (row.wcqk == null || row.wcqk == "") {
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      } else {
        sbwcqkBtnShow.value = false;
      }
      if (row.zblx == "部门自拟指标") {
        sbwcqkBtnShow.value = false;
      }
    } else {
      sbwcqkBtnShow.value = false;
    }
    // 学校仅查看
    if (row.sfbmtxxyjck == "是") {
      sbwcqkBtnShow.value = false;
    } else if (row.sfbmtxxyjck !== "是" && row.zrlx === "自主填报") {
      if (row.wcqk == null || row.wcqk == "") {
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      }
    }
    currentRWData.value = data.map((item) => {
      item.sqsj = item.sqsj.slice(0, 10);
      if (item.fj) {
        fjshow.value = true;
        item.fj = JSON.parse(item.fj);
      }
      return item;
    });
  }
};

const onFy = (uri: string) => {
  const url = uri;
  window.open(url);
};

// 目标情况里的查看详情
const checkPlugin = (guid: any) => {
  const url = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${guid}`;
  window.open(url);
};

const getXYWCQKHtml = async (guid: any, hosttaskid: any) => {
  if (hosttaskid) {
    return false;
  }
  const res = await getRWFJZTById(guid);
  if (res.count > 0) {
    // 查看按钮显示
    return true;
  }
  return false;
};

const exportExcel = () => {
  exportVisible.value = true;
};

const exportSumbit = async () => {
  const obj = {};
  checkList.value.forEach((item: any) => {
    obj[item] = true;
  });
  const url = `http://***********:7005/api/RWDC/ExportDepAssessmentTargetTask?dczd=${JSON.stringify(
    [obj],
  )}&type=部门&departmentName=${
    selectSsbm.value ? selectSsbm.value.ssbm : curdep.value
  }
  &zbrw=${formInline.zbrw}&mbyq=${formInline.mbyq}&zblx=${formInline.zblx}`;
  console.log(url);
  window.open(url);
  exportVisible.value = false;
};

const sendCompete = () => {
  const obj = new Function("return" + rowInfo.value.zbly)();
  const url = `https://ehall.zcmu.edu.cn:5003/iTask/Process210607153632?ZBRWGUID=${rowInfo.value.guid}&ZBRW=${rowInfo.value.zbrw}&MBYQ=${rowInfo.value.mbyq}&ZBLX=${rowInfo.value.zblx}&ZBLY=${obj[0].name}&BZ=${rowInfo.value.bz}&SJRW=${rowInfo.value.sjrw}`;
  window.open(url);
};

const currentEJZB = ref();
const getCurrentEjzb = async () => {
  const res = await getBBMEJZB("");
  currentEJZB.value = res.data;
};

const currentSsmk = ref();
const getcurrentSsmk = async () => {
  const res = await getBBMSSMK();
  currentSsmk.value = res.data;
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
};

const xdbmData = ref<Get_ZBJB_type[]>([]);
const getAllDepe = async () => {
  const { data } = await getAllBM();
  xdbmData.value = data;
};

const handleChange = (value: string) => {
  if (value !== "2024") {
    tableData.value = [];
  } else {
    getAllRW();
  }
};

onMounted(async () => {
  // getAllnd();
  getAllzblx();
  currentYear.value = await getCurYear();
  getcurrentallbmrw();
  getCurrentUserDep();
  getCurrentEjzb();
  getcurrentSsmk();
  getcurrentyjzb();
  getAllDepe();
});
</script>

<style lang="less" scoped>
h2 {
  text-align: center;
}
.left-header {
  text-align: center;
  h3 {
    color: #7a7a7a;
    font-weight: 400;
  }
}
.bottom-plugin {
  padding: 10px;
  margin-top: 200px;
}
h4 {
  padding: 10px;
  color: #2a59b6;
}
.connent {
  padding: 10px;
  border-bottom: 1px solid #dddfe5;
  p {
    margin-top: 10px;
  }
}
.connent:last-child {
  border-bottom: none;
}
.scroll-container {
  width: 100%;
  overflow-x: scroll;
}

.scroll-content {
  display: flex;
  white-space: nowrap;
  height: calc(100vh - 180px); /* 设置容器高度 */
  overflow-y: auto; /* 设置垂直方向滚动条 */
}

/* 可以添加滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #c1c1c1;
}
.header {
  margin: 0px 10px;
  height: 45px;
  .el-form {
    display: flex;
  }
}
.center {
  float: right;
  margin: 0px 10px 10px 10px;
  .text {
    padding-top: 10px;
    span {
      color: rgb(148, 148, 148);
      text-align: center;
    }
  }
}
.tabledata {
  margin: 12px 10px 10px 10px;
}
.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 100px);
}
.el-tag {
  background: #5a9cf8;
  color: #fff;
  margin-right: 5px;
}
.page-fen {
  margin-left: 150px;
}
.el-table {
  height: calc(100vh - 215px);
  border: 1px solid rgb(236, 238, 244);
}
.el-row {
  padding: 15px 20px 15px 20px;
}

/* 更改背景色 */
:deep(.el-popper) {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}

:deep(.el-radio__label) {
  display: none;
}

.el-scrollbar {
  height: calc(100vh - 180px);
}

:deep(.el-dialog__body) {
  padding: 0 20px 20px 20px;
}

.fujian {
  margin-top: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 200px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}
</style>

<!--导航条-->
<template>
  <div class="header-page">
    <!-- 图标 -->
    <div class="frist-img">
      <img src="@/assets/imges/logo2.png" alt="" />
    </div>
    <div class="table-connent">
      <el-menu
        class="el-menu-demo"
        mode="horizontal"
        :default-active="activerouter"
        ellipsis
      >
        <!-- 路由导航 -->
        <template v-if="iscode == false">
          <template v-for="(arr, index) in permissionArr" :key="index">
            <template v-if="arr.code === usercode">
              <el-menu-item
                v-for="v in arr.data"
                :index="v.url"
                :key="v.url"
                @click="selectMenuItem(v.url)"
              >
                {{ v.name }}
              </el-menu-item>
            </template>
          </template>
        </template>
        <template v-else>
          <template v-for="(arr, index) in permissionArr1" :key="index">
            <template v-if="arr.code === usercode">
              <el-menu-item
                v-for="v in arr.data"
                :index="v.url"
                :key="v.url"
                @click="selectMenuItem(v.url)"
              >
                {{ v.name }}
              </el-menu-item>
            </template>
          </template>
        </template>
      </el-menu>
    </div>

    <div class="right-connent">
      <div class="info">{{ currentRoleName }}</div>
      <div class="role">
        <el-dropdown @command="switchRole">
          <span class="el-dropdown-link">切换角色</span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="(item, index) in roleArr"
                :key="item.code"
                :command="{ code: item.code, msg: item.msg }"
              >
                {{ item.msg }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="name">{{ user?.profile.UserName }}</div>
      <div
        class="unlogin-btn"
        @click="signoutRedirect({ post_logout_redirect_uri: '/' })"
        style="cursor: pointer"
      >
        <span>注销</span>
      </div>
      <!-- <div class="last-img">
        <img src="../../../assets/imges/right.png" alt="" />
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getCurrentUserDepName,
  getCurrentUserMenuPermission,
  getisSectionChief,
  getsfwbmfzr,
  getSFWBMZRFZR,
} from "@/api/TYJK";
import { permissionArr } from "@/utils/permissionConfig";
import { onMounted, reactive, ref, watch } from "vue";
import { useAuth } from "vue-oidc-provider";
import { getCurYear } from "@/utils";
import { useRouter } from "vue-router";

const usercode = ref<number | string>(0);
const currentYear = ref<number>();
const useDep = ref<string>("");
const roleArr = reactive([
  {
    code: "",
    msg: "",
  },
]);
const isToken = ref<boolean>(false);
const { user, signoutRedirect } = useAuth();
const activerouter = ref();
const iscode = ref<Boolean>(false);
// const isCollapse = ref<boolean>(false);
const bmfzr = ref();
const bmzyfzr = ref();
const permissionArr1 = [
  {
    code: 50,
    name: "部门普通用户",
    data: [
      { name: "首页", url: "/home" },
      { name: "任务报评", url: "/DeptartmentTargetTaskReport" },
      { name: "学院考核", url: "/ColllegeTargetTaskReport" },
      // { name: "操作手册", url: "https://sjbc.zcmu.edu.cn/mbkh/help.html" },
    ],
  },
];
// 当前选中角色名
const currentRoleName = ref();

// 点击切换当前登录人菜单权限
const switchRole = (command: any) => {
  usercode.value = command.code;
  currentRoleName.value = command.msg;

  window.sessionStorage.setItem("usercode", usercode.value.toString());
  window.sessionStorage.setItem("currentRoleName", currentRoleName.value);

  // 切换角色时转到首页
  router.push("/home");
  activerouter.value = "/home";
  window.sessionStorage.setItem("activerouter", "/home");

  // isCollapse.value = command.code == 20;
};

// 获取当前登录人菜单权限
const getCurrentMenuPermission = async () => {
  let res = await getCurrentUserMenuPermission();
  let code;
  if (window.sessionStorage.getItem("usercode") != null) {
    code = Number(window.sessionStorage.getItem("usercode"));
    currentRoleName.value = window.sessionStorage.getItem("currentRoleName");
  } else {
    code = res[0].code;
    currentRoleName.value = res[0].msg.split("【")[1].split("】")[0];
  }
  for (var i = 0; i < res.length; i++) {
    let dict = {} as any;
    dict.code = res[i].code;
    dict.msg = res[i].msg.split("【")[1].split("】")[0];
    roleArr.push(dict);
  }
  usercode.value = code;
  window.sessionStorage.setItem("usercode", usercode.value.toString());

  if (code == 20) {
    // 隐藏多余选项卡
    // isCollapse.value = true;
  }
  if (code == 50) {
    // if (bmzyfzr.value === true || bmfzr.value === true) {
    //   permissionArr1[0].data.splice(-1, 0, {
    //     name: "审批中心",
    //     url: "/ApprovalCenter",
    //   });
    // }
    iscode.value = await getisSectionChief();
  }
  // if (bmzyfzr.value === true || bmfzr.value === true) {
  //   permissionArr.forEach((item) => {
  //     item.data.splice(-1, 0, {
  //       name: "审批中心",
  //       url: "/ApprovalCenter",
  //     });
  //   });
  // }
};

// 是否为部门主要负责人
const getSFWBMZRfzr = async () => {
  bmzyfzr.value = await getSFWBMZRFZR();
};

// 是否为部门负责人
const getbmfzr = async () => {
  bmfzr.value = await getsfwbmfzr();
};

// 获取当前登陆人的部门名称
const getCurrentUserDepname = async () => {
  useDep.value = await getCurrentUserDepName();
};

const router = useRouter();

watch(isToken, async () => {
  await getCurrentMenuPermission();
  await getCurrentUserDepname();
  await getSFWBMZRfzr();
  await getbmfzr();
  currentYear.value = await getCurYear();
  clearInterval(sh);
});

let sh = setInterval(() => {
  if (user.value?.access_token) {
    isToken.value = true;
  }
}, 1);

const selectMenuItem = (path: any) => {
  if (path.includes("http")) {
    window.open(path);
  } else {
    // 党支部权限加上路由参数 因为后面要用到code
    if (usercode.value == 100) {
      router.push({ path, query: { code: 100 } });
      activerouter.value = path;
      window.sessionStorage.setItem("activerouter", path);
      return;
    }
    // 任务负责人权限
    if (usercode.value == 70) {
      router.push({ path, query: { code: 70 } });
      activerouter.value = path;
      window.sessionStorage.setItem("activerouter", path);
      return;
    }
    router.push(path);
    activerouter.value = path;
    window.sessionStorage.setItem("activerouter", path);
  }
};

onMounted(() => {
  activerouter.value = window.sessionStorage.getItem("activerouter");
});
</script>

<style lang="less" scoped>
.header-page {
  display: flex;
  width: 100%;
  justify-content: space-between;
  .frist-img {
    // width: 240px;
    height: 35px;
    line-height: 60px;
  }

  .table-connent {
    .el-menu {
      background-color: #135abc;
      width: 1100px;
      border: 0;
      height: 60px;
      .el-menu-item {
        color: #fff;
        font-size: 14px;
        font-weight: bolder;
        margin: 0;
        &:hover {
          color: #fff;
          background-color: #2196f3;
        }
      }
    }
  }
  .right-connent {
    display: flex;
    .info {
      width: 125px;
      height: 60px;
      line-height: 60px;
      color: #fff;
      font-size: 16px;
    }
    .role {
      width: 100px;
      height: 60px;
      line-height: 60px;
      color: #fff;
      font-size: 16px;
      .el-dropdown-link {
        cursor: pointer;
        height: 60px;
        line-height: 60px;
        color: #fff;
        font-size: 16px;
        align-items: center;
      }
    }
    .name {
      color: #fff;
      width: 50px;
      height: 40px;
      line-height: 60px;
      font-size: 16px;
      margin-left: 10px;
    }
    .unlogin-btn {
      width: 50px;
      height: 60px;
      line-height: 60px;
      margin-left: 20px;
      color: #fff;
      font-size: 16px;
    }
    .last-img {
      width: 175px;
      height: 60px;
      img {
        width: 100%;
        height: 100%;
        // margin-left:27px;
      }
    }
  }
}
.el-menu-item.is-active {
  background-color: #2196f3 !important;
  color: #fff !important;
}

.el-dropdown-item.is-active {
  background-color: #67c23a; /* 浅绿色背景 */
  color: #67c23a; /* 绿色文字 */
}
</style>
@/utils/permissionConfig

<template>
  <el-row style="padding: 0">
    <el-col :span="19">
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
          @row-click="handleRowClick"
          :row-key="(row: any) => row.guid"
          highlight-current-row
          v-loading="loading"
          :cell-style="columnStyle"
        >
          <el-table-column width="50px" align="center" fixed>
            <template #default="{ row }">
              <el-radio v-model="radio" :label="row.guid"></el-radio>
            </template>
          </el-table-column>
          <el-table-column
            prop="zbrw"
            label="目标任务"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag v-if="row.zrlx == '系统集成'" style="border: none">
                系统抽
              </el-tag>
              <span>{{ row.zbrw }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="mbyq"
            label="目标要求"
            width="200"
            sortable
            show-overflow-tooltip
          />
          <el-table-column
            prop="zblx"
            label="指标类型"
            width="90"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ssbm"
            label="责任部门"
            width="90"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ssbm"
            label="完成学院"
            width="100"
            align="center"
            show-overflow-tooltip
            v-if="cccc == null"
          />
          <el-table-column
            prop="bmgxypj"
            label="给学院评价"
            width="120"
            sortable
            align="center"
            show-overflow-tooltip
            :formatter="driverNameFormat"
          >
            <template #default="{ row }">
              <el-select
                v-if="props.tbcurrentClick.fzrxm == user?.profile.UserName"
                v-model="row.bmgxypj"
                placeholder="请选择"
                @visible-change="handleSelect(row)"
                @change="selectValue(row)"
              >
                <el-option
                  v-for="item in XYPJData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="wcqk"
            label="完成情况（单击编辑）"
            width="300"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-input
                size="small"
                v-model="row.wcqk"
                @blur="saveEdit(row)"
                @keyup.enter="saveEdit(row)"
              />
              <el-tag v-if="row.zrlx == '系统集成'" style="border: none">
                系统抽
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-col>
    <el-col :span="5">
      <div class="left-header">
        <h2>目标完成情况</h2>
        <span>{{ currentClickRw }}</span>
      </div>
      <el-divider style="margin: 10px 0" />
      <el-scrollbar>
        <div v-if="currentBCSM !== ''">
          <div class="bottom-plugin">
            <el-divider style="margin: 10px 0" />
            <h2>补充说明情况</h2>
            <el-divider style="margin: 10px 0" />
            <span>{{ delHtmlTag(currentBCSM) }}</span>
          </div>
        </div>
        <div class="connent" v-else>
          <span v-if="isShow">修订时间：{{ currentWCS }}</span>
          <br />
          <span v-if="isShow">自评结果：{{ currentzpjg }}</span>
          <p>{{ currentWCQK }}</p>
          <el-button
            type="primary"
            size="small"
            style="margin-top: 10px"
            @click="checkPlugin"
            v-if="checkPluginData !== null && checkPluginData !== ''"
          >
            查看详情
          </el-button>
          <div class="fujian">
            <h3 v-if="currentFJ !== null">附件：</h3>
            <div class="fujian-plugin">
              <a
                @click="onFy(item.uri)"
                style="cursor: pointer"
                v-for="(item, index) of currentFJ"
                :key="index"
              >
                {{ item.name }}
              </a>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import {
  getCOLLEGEDETAILByTaskId,
  getWCQKByRWId,
  getBCSMById,
  saveWCQK,
  bmtxSaveEvaluate,
} from "@/api/TYJK/index";
import { XYPJSelect } from "@/utils/permissionConfig";
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { useAuth } from "vue-oidc-provider";
const { user } = useAuth();
const editingRow = ref(null);
const wcqkArr = ref([]);
const columnStyle = ({ columnIndex, row }: any) => {
  if (row.bmgxypj !== null && row.bmgxypjfbzt == "已发布" && columnIndex == 5) {
    return {
      color: "green",
    };
  }
  if (row.bmgxypj == null && columnIndex == 5) {
    return {
      color: "red",
    };
  }
  if (row.bmgxypjfbzt !== "已发布" && columnIndex == 5) {
    return {
      color: "red",
    };
  }
};

const driverNameFormat = (row: any) => {
  return row.bmgxypj == null ? "未评价" : row.bmgxypj;
};

const XYPJData = ref<any>({
  label: "",
  value: "",
});

// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
const checkPluginData = ref();
const currentClickRw = ref<string>("");
const currentWCS = ref<string>("");
const currentWCQK = ref<string | null>("");
const currentFJ = ref({
  item: {
    name: "",
    uri: "",
  },
});
const currentBCSM = ref<string | null>("");
const isShow = ref<boolean>(false);
const props = defineProps(["tbcurrentClick"]);
const radio = ref<boolean>(false);
const loading = ref<boolean>(false);
const rowInfo = ref(null);
const tableData = ref<GET_RWFJZT_Type[]>([]);
const cccc = ref();
// 获取学校目标情况分解任务
const getCollegeCompletionDetail = async () => {
  try {
    loading.value = true;
    const { data } = await getCOLLEGEDETAILByTaskId(props.tbcurrentClick.guid);
    console.log(data);
    cccc.value = data[0].sfbmtxxyjck;
    tableData.value = data;
  } finally {
    loading.value = false;
  }
};
const currentzpjg = ref();
// 单击每一行任务
const handleRowClick = async (row: any) => {
  radio.value = row.guid;
  rowInfo.value = row;
  currentClickRw.value = row.zbrw;
  if (row.zrlx == "系统集成") {
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
  } else {
    const { data } = await getWCQKByRWId(row.guid);
    const aaa = data[0].fj;
    checkPluginData.value = data[0];
    if (checkPluginData.value !== undefined) {
      currentWCS.value = data[0].wcsj.slice(0, 10);
      currentWCQK.value = data[0].wcqk;
      currentzpjg.value = data[0].zpjg;
      currentFJ.value = JSON.parse(data[0].fj);
      isShow.value = true;
    }
  }
};

const checkPlugin = () => {
  const url = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${checkPluginData.value.guid}`;
  window.open(url);
};

const onFy = (uri: string) => {
  const url = `https://ydmh.guangshaxy.com/formWebSite/form/${uri}`;
  window.open(url);
};

const handleSelect = (row: any) => {
  XYPJSelect.forEach((item: any) => {
    if (row.zblx == item.zblx) {
      XYPJData.value = item.nzpj;
    }
  });
};

const selectValue = async (row: any) => {
  const { code } = await bmtxSaveEvaluate(row.guid, row.xyzp);
  if (code === 0) {
    ElMessage.success("编辑成功");
  }
};

const saveEdit = async (row: any) => {
  editingRow.value = null;
  wcqkArr.value = row;
  const { code } = await saveWCQK([wcqkArr.value]);
  if (code == 0) {
    ElMessage.success("编辑成功");
  }
};

onMounted(() => {
  getCollegeCompletionDetail();
});
</script>

<style lang="less" scoped>
.el-table {
  height: 500px;
  border: 1px solid rgb(236, 238, 244);
}
/deep/.el-row {
  padding: 0;
}
.left-header {
  text-align: center;
  h3 {
    color: #7a7a7a;
    font-weight: 400;
  }
}
.bottom-plugin {
  padding: 10px;
  margin-top: 200px;
}
h2 {
  text-align: center;
}
h4 {
  padding: 10px;
  color: #2a59b6;
}
.connent {
  padding: 10px;
  p {
    margin-top: 10px;
  }
}
.el-col-5 {
  border: 1px solid rgb(236, 238, 244);
}
.el-scrollbar {
  height: 390px;
}
.table {
  margin-right: 10px;
}
.el-tag {
  background: #5a9cf8;
  color: #fff;
  margin-right: 5px;
}
.fujian {
  margin-top: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 150px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}
</style>

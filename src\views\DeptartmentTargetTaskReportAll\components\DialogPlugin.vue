<template>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column prop="xyzp" label="评分" width="120" align="center">
      <template #default="{ row }">
        <el-input
          v-model="row.xyzp"
          placeholder="请输入数字"
          @change="(val: number) => onChangeInput(val, row)"
        />
      </template>
    </el-table-column>
    <!-- <el-table-column prop="zbrwfz" label="分值" width="120" align="center" /> -->
    <el-table-column prop="zbrw" label="指标内容" />
    <el-table-column prop="mbyq" label="评分办法" />
    <el-table-column label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button type="danger" @click="bgReportingOnclick(row)">
          报评
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="1200"
    hight="700px"
    top="2vh"
    destroy-on-close
  ></el-dialog>
</template>

<script lang="ts" setup>
import { getTGSJCX, bmtxSaveEvaluate } from "@/api/TYJK/index";
import { ref, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
const props = defineProps(["bgdialogTitle"]);
const emits = defineEmits(["onDialogVisibleSubmit"]);
const dialogTitle = ref();
const dialogVisible = ref(false);
const tableData = ref<GET_RWFJZT_Type[]>([]);
const bgReportingOnclick = (row: any) => {
  dialogTitle.value = row.zbrw;
  window.open(
    `https://***********:5003/iForm/191103B9A95CF0CB209403?MBRWGUID=${row.guid}`,
  );
};

const getCurrentsjzb = async () => {
  const res = await getTGSJCX(props.bgdialogTitle);
  tableData.value = res.data;
};

// 修改数字
const onChangeInput = async (val: any, row: any) => {
  row.xyzp = parseFloat(val);
  const res = await bmtxSaveEvaluate(row.guid, row.xyzp);
  if (res.code === 200 && res.count === 1) {
    ElMessage.success("编辑成功");
  }
  emits("onDialogVisibleSubmit", xyzpAll.value, props.bgdialogTitle);
};

const xyzpAll = computed(() => {
  let sum = 0;
  for (let i = 0; i < tableData.value.length; i++) {
    if (tableData.value[i].xyzp === null) continue;
    sum += parseFloat(tableData.value[i].xyzp);
  }
  return sum;
});

onMounted(() => {
  getCurrentsjzb();
});
</script>

<style lang="less" scoped>
.el-table {
  border: 1px solid rgb(236, 238, 244);
}
</style>

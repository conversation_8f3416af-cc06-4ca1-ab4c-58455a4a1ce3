import request from "@/utils/request";
enum API {
  GET_BMKH_MBND = "/api/BMKH/getZRBMByND",
  GET_EJXY_BYND = "/api/XYKH/getEJXYByND",
  Get_College_Completion_Detail_By_Task_Id = "/api/BMKH/GetCollegeCompletionDetailByTaskId",
  Update_XZCS = "/api/XYKH/UpdateXZCS",
}
//获取部门目标任务
export const getBMND = (nd: string) =>
  request.get<unknown, Result<Array<GET_BMMB_Type>>>(API.GET_BMKH_MBND, {
    params: { nd },
  });

// 根据年度查询对应二级学院
export const getEJXYByND = (nd: string) =>
  request.get<unknown, Result<Array<GET_BMMB_Type>>>(API.GET_EJXY_BYND, {
    params: { nd },
  });

export const getCollegeCompletionDetailByTaskId = (guid: string) => {
  return request.get<
    unknown,
    Result<Array<GetCollegeCompletionDetailByTaskIdResult>>
  >(API.Get_College_Completion_Detail_By_Task_Id, {
    params: {
      guid,
    },
  });
};

export const updateXZCS = (guid: string) => {
  return request.post<unknown, Result<null>>(API.Update_XZCS, null, {
    params: { guid },
  });
};

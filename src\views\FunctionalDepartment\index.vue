<!-- 任务分解 -->
<template>
  <div>
    <div class="header">
      <div class="form-left">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <!-- <el-form-item label="一级指标">
            <el-select
              v-model="formInline.yjzb"
              placeholder="请选择"
              clearable
              @change="(val:string) => changeYjzb(val)"
            >
              <el-option
                v-for="item in currentYjzb"
                :key="item.guid"
                :label="item.yjzb"
                :value="item.yjzb"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="二级指标">
            <el-select v-model="formInline.ejzb" placeholder="请选择" clearable>
              <el-option
                v-for="item in currentEJZB"
                :key="item.guid"
                :value="item.ejzb"
                :label="item.ejzb"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="年度">
            <el-select v-model="year" @change="handleChange">
              <el-option label="2025" value="2025"></el-option>
              <el-option label="2024" value="2024"></el-option>
              <el-option label="2023" value="2023"></el-option>
              <el-option label="2022" value="2022"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="指标等级">
            <el-input
              v-model="formInline.sjzb"
              placeholder="请输入"
              @keyup.enter="searchData"
            />
          </el-form-item>
          <el-form-item label="下达部门">
            <el-select v-model="formInline.xdbm" placeholder="请选择" clearable>
              <el-option
                v-for="item in xdbmData"
                :key="item.departmentid"
                :label="item.departmentname"
                :value="item.departmentname"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="searchData">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="getAllData">全部</el-button>
          </el-form-item>
          <el-form-item label="批量分配">
            <el-select
              v-model="username"
              filterable
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in headList"
                :key="item.userid"
                :label="item.username"
                :value="item.userid"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="right-btn" @click="lotSave">
              批量保存
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="center-btn">
      <el-button type="primary" @click="taskSearch()">任务分解/查看</el-button>
    </div>
    <div class="tabledata">
      <el-table
        ref="multipleTableRef"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading"
        :row-key="
          (row: any) => {
            return row.guid;
          }
        "
        highlight-current-row
      >
        <el-table-column
          type="selection"
          width="55"
          :reserve-selection="true"
        />
        <!-- <el-table-column
          property="yjzb"
          label="一级指标"
          sortable
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          property="ejzb"
          label="二级指标"
          sortable
          show-overflow-tooltip
        >
        </el-table-column> -->
        <el-table-column
          property="sjzb"
          label="指标等级"
          sortable
          show-overflow-tooltip
        />
        <el-table-column label="考核内容及标准" sortable show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag
              v-if="row.sfbmtxxyjck == '是' && row.zrlx == '自主填报'"
              style="border: none; background-color: orange; color: #fff"
              size="small"
            >
              下达部门填
            </el-tag>
            <span>{{ row.zbrw }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="sjzb"
          label="指标等级"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          property="mbyq"
          label="评分办法"
          sortable
          show-overflow-tooltip
        />
        <!--        <el-table-column-->
        <!--          property="ssbm"-->
        <!--          label="所属部门"-->
        <!--          sortable-->
        <!--          width="150"-->
        <!--          align="center"-->
        <!--          show-overflow-tooltip-->
        <!--        />-->

        <el-table-column
          property="xdbm"
          label="下达部门"
          sortable
          width="150"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          property="fzrxm"
          label="负责人"
          sortable
          width="200"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-select
              v-model="row.fzrxm"
              placeholder="请选择"
              @change="(value: string[]) => selectValue(row, value)"
              multiple
              filterable
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="item in headList"
                :key="item.userid"
                :label="item.username"
                :value="item.username"
              />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="right-page">
      <!-- 底部分页 -->
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
        layout=" prev, pager, next, jumper,total, sizes"
        :total="total"
        class="page-fen"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      v-model="dialogFormVisible"
      width="1350px"
      title="任务分解/查看"
      top="60px"
    >
      <DialogPlugin />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  getTeacherInfo,
  getCurrentYearRWByDepartmentName2,
  saveFzrBySelect,
  saveFzr,
  getBBMEJZB,
  getBMYJZB,
  getAllDep,
} from "@/api/TYJK/index";
import { reactive, ref } from "vue";
import DialogPlugin from "./components/DialogPlugin.vue";
import { onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
const route = useRoute();

const multipleTableRef = ref();
const username = ref<string>("");
const multipleSelection = ref<GET_RWFJZT_Type[]>([]);
const dialogFormVisible = ref(false);
const loading = ref<boolean>(false);
const tableData = ref<GET_RWFJZT_Type[]>([]);
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const formInline = reactive({
  yjzb: "",
  ejzb: "",
  sjzb: "",
  zblx: "",
  ssbm: "",
  xdbm: "",
});
const headList = ref<Array<GET_TEACHERINFO_Type>>([]);
const year = ref<string>("2024");

const handleSelectionChange = (val: GET_RWFJZT_Type[]) => {
  multipleSelection.value = val;
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  getCurrentYearDepartmentName();
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  getCurrentYearDepartmentName();
};

const selectValue = async (row: GET_RWFJZT_Type, userNames: string[]) => {
  const saveData = userNames.map((item) =>
    headList.value.find((head) => head.username === item),
  );

  const gh = saveData.map((item) => item!.userid).join(",");
  const xm = saveData.map((item) => item!.username).join(",");

  const { code } = await saveFzrBySelect(row.guid, gh, xm);
  if (code === 0) {
    getCurrentYearDepartmentName();
    ElMessage.success("编辑成功");
  }
};

// 任务分解/查看按钮
const taskSearch = () => {
  dialogFormVisible.value = true;
};

// 批量保存
const lotSave = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessageBox.alert("请先选择目标任务", "信息", {
      confirmButtonText: "确定",
    });
  } else {
    const checkedGUID = multipleSelection.value
      .map((item) => item.guid)
      .join("|");
    const person = headList.value.find(
      (item) => item.userid === username.value,
    )!;
    const { code } = await saveFzr(checkedGUID, person.userid, person.username);
    if (code == 200) {
      ElMessage.success("编辑成功");
    }
    getCurrentYearDepartmentName();
    multipleTableRef.value.clearSelection();
  }
};

const xdbmData = ref<GET_COLLEGEINFO_Type[]>([]);
const getAllDepe = async () => {
  const { data } = await getAllDep();
  xdbmData.value = data;
};

// 搜索
const searchData = async () => {
  try {
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      ssbm: formInline.ssbm,
      xdbm: formInline.xdbm,
    };
    loading.value = true;
    const res = await getCurrentYearRWByDepartmentName2(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
    );
    if (res.data) {
      total.value = res.count;
      handleTableData(res.data);
    } else {
      total.value = 0;
      tableData.value = []; // 清空表格数据
    }
  } finally {
    loading.value = false;
  }
};

const changeYjzb = async (val: string) => {
  const res = await getBBMEJZB(val);
  currentEJZB.value = res.data;
};

// 全部按钮
const getAllData = async () => {
  try {
    const requestInfo = {
      ssbm: formInline.ssbm,
    };
    loading.value = true;
    const res = await getCurrentYearRWByDepartmentName2(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
    );
    if (res.data) {
      total.value = res.count;
      handleTableData(res.data);
    }
  } finally {
    loading.value = false;
  }
};

const getCurrentYearDepartmentName = async () => {
  try {
    loading.value = true;
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      ssbm: formInline.ssbm,
      xdbm: formInline.xdbm,
    };
    const { data, count } = await getCurrentYearRWByDepartmentName2(
      page.value,
      limit.value,
      JSON.stringify(requestInfo),
    );
    if (data) {
      total.value = count;
      handleTableData(data);
    }
  } finally {
    loading.value = false;
  }
};

const handleTableData = (data: any) => {
  tableData.value = data.map((item: any) => {
    return {
      ...item,
      fzrxm: item.fzrxm ? item.fzrxm.split(",") : [],
    };
  });
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
};

const currentEJZB = ref();
const getCurrentEjzb = async () => {
  const res = await getBBMEJZB("");
  currentEJZB.value = res.data;
};

const handleChange = (value: string) => {
  if (value !== "2024") {
    tableData.value = [];
  } else {
    searchData();
  }
};

onMounted(async () => {
  getAllDepe();
  getCurrentYearDepartmentName();
  getcurrentyjzb();
  getCurrentEjzb();
  const { data } = await getTeacherInfo(route.query.code as string);
  headList.value = data;
});
</script>

<style lang="less" scoped>
.header {
  display: flex;
  justify-content: space-between;
  .form-left {
    // width: 1000px;
    height: 55px;
  }
  .form-right {
    margin-top: 15px;
    .right-btn {
      margin-left: 20px;
      margin-right: 20px;
    }
  }
}
.el-form {
  display: flex;
  margin-top: 15px;
  margin-left: 20px;
}
.center-btn {
  float: right;
  margin-right: 20px;
}
.tabledata {
  margin-top: 40px;
  margin-left: 20px;
  margin-right: 20px;
}
/* 更改背景色 */
:deep(.el-popper) {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}
.page-fen {
  margin-left: 150px;
  margin-top: 10px;
}
.el-table {
  user-select: text;
  height: calc(100vh - 207px);
  border: 1px solid rgb(236, 238, 244);
}
.el-scrollbar {
  height: calc(100vh - 210px);
}
:deep(.el-dialog__body) {
  padding: 2px 10px 10px 20px;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #0ebb9e81 !important;
}
</style>

import { updataYCZT } from './index';
import request from "@/utils/request";
enum API {
  GET_RWKALLND = "/api/TYJK/GetRWKALLNd",
  GET_CURYERRALLZNBMRW = "/api/TYJK/getCurrentYearAllZNBMRW",
  getCurrentUserMenuPermission = "/api/TYJK/getCurrentUserMenuPermission",
  ADD_RW = "/api/TYJK/AddRW",
  EDIT_RW = "/api/TYJK/editRW",
  DELETE_RW = "/api/TYJK/DeleteRW",
  GET_CURRENT_YEAR = "/api/TYJK/getCurrentYear",
  GET_RWZBLX_BYND = "/api/TYJK/getRWKZBLXByNd",
  GET_COLLEGE_TASK = "/api/XYKH/getAllCollegeTargetTaskKH",
  GET_WCQK_BYRWID = "/api/TYJK/getWCQKByRWId",
  GET_BCSM_BYID = "/api/TYJK/getRWBCSMById",
  GET_RW_BYID = "/api/TYJK/getRWById",
  GET_RWFJZT_BYID = "/api/TYJK/getRWFJZTById",
  GET_COLLEGEDETAIL_BYTASKID = "/api/BMKH/GetCollegeCompletionDetailByTaskId",
  GET_CURRENT_USERDEPNAME = "/api/XYKP/getCurrentUserDepartmentname",
  GET_TEACHER_INFO = "/api/TYJK/getTeacherInfo",
  GET_XYKP_ALLTASK = "api/XYKP/getXYKPAllCollegeTargetTask",
  GET_COLLEGEINFO = "/api/TYJK/getAllCollegeInfo",
  GET_CURRENT_DEPARTMENTNAME = "/api/RWBP/getCurrentYearRWByDepartmentName",
  GET_CURRENT_DEPARTMENTNAME2 = "api/RWFJ/getCurrentYearRWByDepartmentName",
  POST_SAVEBMYEAREND_EVALUSTE = "/api/TYJK/saveBmYearEndEvaluate",
  POST_SAVEFZR_BYSELECT = "/api/TYJK/saveFzrBySelect",
  GET_CURRENTALLRW_DEPARTMENTNAME = "/api/TYJK/getCurrentYearAllRWByDepartmentName",
  POST_SAVEDECOMPOSITION = "/api/RWFJ/saveDecomposition",
  GET_ZRWEJZT_BYID = "/api/RWFJ/getZRWFJZTById",
  POST_TASKDECOMPOSITION = "/api/RWFJ/taskDecomposition",
  GET_ALLSUBTASK_BYTASKID = "/api/RWFJ/getAllSubtaskByTaskId",
  GET_ZBLX = "/api/LHZB/GetZBLX",
  POST_ADDLHZB = "/api/LHZB/AddLHZB",
  GET_BRFZRWZBS = "/api/RWBP/Getbrfzrwzbs",
  GET_ZBSHQK = "/api/RWBP/Getzbshqk",
  EXPORT_DEPTARTASK = "/api/RWDC/ExportDepAssessmentTargetTask",
  GET_CURENTDEPID = "/api/TYJK/getCurrentUserDepartmentid",
  SAVE_WCQK = "/api/RWBP/saveWCQK",
  SAVE_FZR = "/api/RWFJ/saveFzr",
  BMEXPORTTARTASK = "/api/RWDC/BMExportTargetTask",
  EXPORT_COLLEGETASK = "/api/RWDC/ExportCollegeTargetTask",
  EDIT_MBYQ = "/api/TYJK/EditMbyq",
  GET_FUNDEPTARTASK = "/api/TYJK/getAllFunctionalDepartmentTargetTask",
  POST_BMTXSAVEEVALUATE = "/api/TYJK/bmtxsaveEvaluate",
  GET_CHECKREPORTTASK = "/api/RWBP/checkReportTask",
  POST_SAVEBMGXYPJ = "/api/XYKP/saveBMGXYPJ",
  GET_ALLDEP = "/api/RWFJ/getAllDepartement",
  Get_DateNow = "/api/TYJK/GetDateNow",
  GET_HQWCQK = "/api/TYJK/HQWCQK",
  POST_SaveRWBCSM = "/api/TYJK/SaveRWBCSM",
  GET_Rwmrfzr = "/api/TYJK/GetRwmrfzr",
  GET_isSectionChief = "/api/TYJK/isSectionChief",
  GET_CurrentYearRWByCollegeName = "/api/RWBP/getCurrentYearRWByCollegeName",
  GET_SFWBMZRFZR = "/api/TYJK/SFWBMZRFZR",
  GET_sfwbmfzr = "/api/TYJK/sfwbmfzr",
  GET_TGSJCX = "/api/TYJK/TGSJCX",
  Get_BBMEJZB = "/api/RWBP/GetBBMEJZB", //二级指标
  Get_BBMSSMK = "/api/RWBP/GetBBMSSMK", //所属模块
  Get_BBMSJZB = "/api/RWBP/GetBBMSJZB", //三级指标
  Get_BBMYJZB = "/api/RWBP/GetBBMYJZB", //一级指标
  Get_BMXY = "/api/XYKP/GetBMXY",
  GET_getAllFunctionalDepartmenttask = "/api/TYJK/getAllFunctionalDepartmenttask",
  Add_QnZB = "/api/TYJK/CSTJRW",
  Get_ZBJB = "/api/TYJK/GetZBJB",
  Add_ZBJB = "/api/TYJK/AddZBJB",
  Get_ZBJBXX = "/api/TYJK/GetZBJBXX",
  Update_ZBJB = "/api/TYJK/UpdateZBJB",
  Delete_ZBJB = "/api/TYJK/DeleteZBJB",
  Updata_YCZT = "/api/TYJK/UpYCZT",
  Updata_BCNZPJ = "/api/TYJK/saveBmYearEndEvaluate",
  Get_AllZRXY = "/api/TYJK/GetAllXY",
  Get_AllZRBM = "/api/TYJK/GetAllBM",
  Get_FJZBCX = "/api/RWFJ/GetFJZBCX", // 任务分解-弹框页面搜索接口
  Get_WFJBM = "/api/RWFJ/GetWFJBM",
  Copy_ZBJB = "/api/TYJK/CopyZBJB",
  Update_QY_ZBJB = "/api/TYJK/UpqyZBJB",
}

export const getisSectionChief = () =>
  request.get<unknown, Boolean>(API.GET_isSectionChief);

export const getAllFunDepTargetTask = (
  page: number,
  limit: number,
  searchParams?: string,
  nd?: string,
  code?: string,
) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(API.GET_FUNDEPTARTASK, {
    params: { page, limit, searchParams, nd, code },
  });

// 查询所有年度
export const getRwkallnd = () =>
  request.get<unknown, Result<Array<GET_ALLND_Type>>>(API.GET_RWKALLND);

//查询当前年度
export const getCurrentYear = () =>
  request.get<unknown, number>(API.GET_CURRENT_YEAR);

// 获取当前用户部门名称
export const getCurrentUserDepName = () =>
  request.get<unknown, string>(API.GET_CURRENT_USERDEPNAME);

export const getCurrentUserMenuPermission = () =>
  request.get<unknown, GET_permission_Type[]>(API.getCurrentUserMenuPermission);

// 查询人物库当前年度所有职能部门任务
export const getAllZNBMRW = (
  page: number,
  limit: number,
  searchParams?: string,
  nd?: string,
) =>
  request.get<unknown, Result<Array<GET_ALLZNBMRW_Type>>>(
    API.GET_CURYERRALLZNBMRW +
      `?page=${page}&limit=${limit}&searchParams=${searchParams}&nd=${nd}`,
  );

export const getFunctionalDepartmenttask = (
  page: number,
  limit: number,
  searchParams?: string,
  nd?: string,
) =>
  request.get<unknown, Result<Array<GET_ALLZNBMRW_Type>>>(
    API.GET_getAllFunctionalDepartmenttask +
      `?page=${page}&limit=${limit}&searchParams=${searchParams}&nd=${nd}`,
  );

//查询所有学院目标任务【学院考核页面】
export const getCOLLEGETASK = (
  page: number,
  limit: number,
  searchParams?: string,
  nd?: string,
) =>
  request.get<unknown, Result<Array<GET_ALLZNBMRW_Type>>>(
    API.GET_COLLEGE_TASK,
    { params: { page, limit, searchParams, nd } },
  );
// 添加任务
export const addRW = (data: GET_ADDUPDATERW_Type) =>
  request.post<unknown, Result>(API.ADD_RW, data);

// 编辑任务
export const editRW = (data: GET_ADDUPDATERW_Type) =>
  request.post<unknown, Result>(API.EDIT_RW, data);

// 删除或批量删除任务
export const deleteRW = (guid: string) =>
  request.post<unknown, Result<null>>(API.DELETE_RW, [{ guid: guid }]);

// 根据年度获取任务库中的指标类型
export const getRwzblxBynd = (nd: string, bmid: string) =>
  request.get<unknown, Result<Array<GET_RWBYND_Type>>>(API.GET_RWZBLX_BYND, {
    params: { nd, bmid },
  });

// 根据任务ID获取完成情况
export const getWCQKByRWId = (guid: string) =>
  request.get<unknown, Result<Array<GET_RWQK_Type>>>(API.GET_WCQK_BYRWID, {
    params: { guid },
  });

// 根据任务ID查询任务补充情况
export const getBCSMById = (guid: string) =>
  request.get<unknown, Result<Array<GET_BCSM_Type>>>(API.GET_BCSM_BYID, {
    params: { guid },
  });

//根据guid获取任务信息
export const getRWById = (guid: string) =>
  request.get<unknown, Result<Array<GET_BCSM_Type>>>(API.GET_RW_BYID, {
    params: { guid },
  });

// 根据id判断【主任务】的分解状态（存在一条发布的子任务则为已分解）
export const getRWFJZTById = (guid: string) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(API.GET_RWFJZT_BYID, {
    params: { guid },
  });

// 根据任务ID查询该任务各学院完成情况（最新一条）
export const getCOLLEGEDETAILByTaskId = (guid: string) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(
    API.GET_COLLEGEDETAIL_BYTASKID,
    {
      params: { guid },
    },
  );

// 获取当前登录人所在部门正、副科级教职工信息
export const getTeacherInfo = (code?: string) =>
  request.get<unknown, Result>(API.GET_TEACHER_INFO + `?code=${code}`);

// 【学院考评】查询所有学院目标任务
export const getXYKPAllCollegeTargetTask = (
  page: number,
  limit: number,
  searchParams?: string,
  nd?: string,
  code?: string,
) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(API.GET_XYKP_ALLTASK, {
    params: { page, limit, searchParams, nd, code },
  });

// 查询所有学院下拉框
export const getAllCollegeInfo = () =>
  request.get<unknown, Result<Array<GET_COLLEGEINFO_Type>>>(
    API.GET_COLLEGEINFO,
  );

// 查询当前用户所在部门所有任务
export const getCurrentYearRWByDepartmentName = (
  page?: number,
  limit?: number,
  searchParams?: string,
  nd?: string,
  code?: string,
) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(
    API.GET_CURRENT_DEPARTMENTNAME,
    {
      params: { page, limit, searchParams, nd, code },
    },
  );

// 查询当前用户所在部门所有任务 针对任务分解页面
export const getCurrentYearRWByDepartmentName2 = (
  page?: number,
  limit?: number,
  searchParams?: string,
) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(
    API.GET_CURRENT_DEPARTMENTNAME2,
    {
      params: { page, limit, searchParams },
    },
  );

export const getCurrentYearRWByCollegeName = (
  page?: number,
  limit?: number,
  searchParams?: string,
  nd?: string,
) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(
    API.GET_CurrentYearRWByCollegeName,
    {
      params: { page, limit, searchParams, nd },
    },
  );

// 保存年终评价
export const saveBmYearEndEvaluate = (guid: string, nzpj: string) =>
  request.post<unknown, Result<null>>(
    API.POST_SAVEBMYEAREND_EVALUSTE + `?guid=${guid}&nzpj=${nzpj}`,
  );

export const bmtxSaveEvaluate = (guid: string, xyzp: string) =>
  request.post<unknown, Result<null>>(
    API.POST_BMTXSAVEEVALUATE + `?guid=${guid}&xyzp=${xyzp}`,
  );

// 根据任务guid和下拉框选中的值保存任务负责人
export const saveFzrBySelect = (guid: string, fzrgh: string, fzrxm: string) =>
  request.post<unknown, Result>(
    API.POST_SAVEFZR_BYSELECT + `?guid=${guid}&fzrgh=${fzrgh}&fzrxm=${fzrxm}`,
  );

// 查询当前用户所在部门所有任务
export const getCurrentYearAllRWByDepartmentName = (searchParams?: string) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(
    API.GET_CURRENTALLRW_DEPARTMENTNAME,
    {
      params: { searchParams },
    },
  );

// 职能部门任务保存和添加
export const saveDecomposition = (data: GET_BCSM_Type) =>
  request.post<unknown, Result>(API.POST_SAVEDECOMPOSITION, data);

// 根据任务id判断【子任务】是否分解
export const getZRWFJZTById = (guid: string) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(API.GET_ZRWEJZT_BYID, {
    params: { guid },
  });

//职能部门任务修改和确认分解
export const taskDecomposition = (data: GET_BCSM_Type) =>
  request.post<unknown, Result>(API.POST_TASKDECOMPOSITION, data);

// 根据主任务id查询所有子任务信息
export const getAllSubtaskByTaskId = (guid: string) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(
    API.GET_ALLSUBTASK_BYTASKID,
    {
      params: { guid },
    },
  );

// 获取指标类型量化规则
export const getZBLX = (lxmc: string) =>
  request.get<unknown, Result>(API.GET_ZBLX + `?lxmc=${lxmc}`);

// 量化指标保存
export const AddLHZB = (data: POST_ADDLHZB_Type) =>
  request.post<unknown, Result>(API.POST_ADDLHZB, data);

// 获取本人指标任务数量
export const getbrfzrwzbs = () =>
  request.get<unknown, Result>(API.GET_BRFZRWZBS);

// 获取指标审核情况
export const getzbshqk = (bmmc: string) =>
  request.get<unknown, Result>(API.GET_ZBSHQK + `?bmmc=${bmmc}`);

// 部门考核导出
export const exportDepTargetTask = (
  dczd: string,
  type: string,
  departmentName?: string,
  zbrw?: string,
  mbyq?: string,
  zblx?: string,
  fzrxm?: string,
) =>
  request.get<unknown, Result>(
    API.EXPORT_DEPTARTASK +
      `?dczd=${dczd}&type=${type}&departmentName=${departmentName}&zbrw=${zbrw}&mbyq=${mbyq}&zblx=${zblx}&fzrxm=${fzrxm}`,
  );

// 通过登录人获取部门ID
export const getCurrentUserDepID = () =>
  request.get<unknown, string>(API.GET_CURENTDEPID);

// 保存完成情况
export const saveWCQK = (data: GET_BCSM_Type) =>
  request.post<unknown, Result>(API.SAVE_WCQK, data);

//批量保存负责人
export const saveFzr = (guid: string, fzrgh: string, fzrxm: string) =>
  request.post<unknown, Result>(
    API.SAVE_FZR + `?guid=${guid}&fzrgh=${fzrgh}&fzrxm=${fzrxm}`,
  );

// 任务报评导出
export const exportTargetTask = (
  dczd: string,
  type: string,
  departmentName?: string,
  zbrw?: string,
  mbyq?: string,
  zblx?: string,
  fzrxm?: string,
  xyzpfbzt?: string,
) =>
  request.get<unknown, Result>(
    API.BMEXPORTTARTASK +
      `?dczd=${dczd}&type=${type}&departmentName=${departmentName}&zbrw=${zbrw}&mbyq=${mbyq}&zblx=${zblx}&fzrxm=${fzrxm}&xyzpfbzt=${xyzpfbzt}`,
  );

// 学院考评导出
export const exportCollegeTargetTask = (
  dczd: string,
  departmentName: string,
  zbrw?: string,
  mbyq?: string,
  zblx?: string,
  fzrxm?: string,
  ssbm?: string,
) =>
  request.get<unknown, Result>(
    API.EXPORT_COLLEGETASK +
      `?dczd=${dczd}&departmentName=${departmentName}&zbrw=${zbrw}&mbyq=${mbyq}&zblx=${zblx}&fzrxm=${fzrxm}&ssbm=${ssbm}`,
  );

// 修改目标要求
export const EditMbyq = (guid: string, mbyq: string) =>
  request.post<unknown, Result>(API.EDIT_MBYQ + `?guid=${guid}&mbyq=${mbyq}`);

export const checkReportTask = (guid: string) =>
  request.get<unknown, string>(API.GET_CHECKREPORTTASK + `?guid=${guid}`);

export const saveBMGXYPJ = (guid: string, bmgxypj: string) =>
  request.post<unknown, Result>(
    API.POST_SAVEBMGXYPJ + `?guid=${guid}&bmgxypj=${bmgxypj}`,
  );

export const getAllDep = () =>
  request.get<unknown, Result<Array<GET_COLLEGEINFO_Type>>>(API.GET_ALLDEP);

export const getNowDate = () => request.get<unknown, string>(API.Get_DateNow);

export const getHQWCQK = (guid: string, ssbm: string) =>
  request.get<unknown, any>(API.GET_HQWCQK + `?guid=${guid}&ssbm=${ssbm}`);

export const saveRWBCSM = (guid: string, bcsm: string) =>
  request.post<unknown, Result>(
    API.POST_SaveRWBCSM + `?guid=${guid}&bcsm=${bcsm}`,
  );

// 获取当前登陆人是否为任务默认负责人
export const getRwmrfzr = () => request.get<unknown, Boolean>(API.GET_Rwmrfzr);

export const getSFWBMZRFZR = () =>
  request.get<unknown, Boolean>(API.GET_SFWBMZRFZR);

export const getsfwbmfzr = () =>
  request.get<unknown, Boolean>(API.GET_sfwbmfzr);

export const getTGSJCX = (sjzb: string) =>
  request.get<unknown, Result>(API.GET_TGSJCX + `?sjzb=${sjzb}`);

export const getBBMEJZB = (yjzb?: string) =>
  request.get<unknown, Result>(API.Get_BBMEJZB + `?yjzb=${yjzb}`);

export const getBBMSSMK = () => request.get<unknown, Result>(API.Get_BBMSSMK);

export const getBBMSJZB = (guid: string) =>
  request.get<unknown, Result<any>>(API.Get_BBMSJZB + `?guid=${guid}`);

export const getBMYJZB = () => request.get<unknown, Result>(API.Get_BBMYJZB);

export const getBMXY = () => request.get<unknown, Result>(API.Get_BMXY);

export const addzb = (guid: string) =>
  request.post<unknown, Result>(API.Add_QnZB + `?guid=${guid}`);

export const getZBJB = () =>
  request.get<unknown, Result<Get_ZBJB_type[]>>(API.Get_ZBJB);
export const addZBJB = (mc: string, id?: string) =>
  request.post<unknown, Result<any>>(API.Add_ZBJB + `?mc=${mc}&id=${id}`);

export const getZBJBXX = (id: string) =>
  request.get<unknown, Result<Get_ZBJB_type[]>>(API.Get_ZBJBXX + `?id=${id}`);

// 编辑指标级别
export const updateZBJB = (id: string, mc: string) =>
  request.post<unknown, Result<any>>(API.Update_ZBJB + `?id=${id}&mc=${mc}`);

// 删除指标级别
export const Delete_ZBJB = (id: string) =>
  request.post<unknown, Result<any>>(API.Delete_ZBJB + `?id=${id}`);

// 修改指标锁定状态
export const updataYCZT = (guid: string, sfyc: string) =>
  request.post<unknown, Result<any>>(
    API.Updata_YCZT + `?guid=${guid}&sfyc=${sfyc}`,
  );

export const getNZPJ = (guid: string, bmgxypj: string) =>
  request.post<unknown, Result<Array<GET_BCSM_Type>>>(
    API.Updata_BCNZPJ + `?guid=${guid}&bmgxypj=${bmgxypj}`,
  );

// 获取全部部门
export const getAllBM = () =>
  request.get<unknown, Result<Get_ZBJB_type[]>>(API.Get_AllZRBM);

// 获取全部学院
export const getAllXY = () =>
  request.get<unknown, Result<Get_ZBJB_type[]>>(API.Get_AllZRXY);

// 任务分解-弹框页面搜索
export const GetFJZBCX = (keyWord: string) =>
  request.get<unknown, Result<Array<GET_RWFJZT_Type>>>(API.Get_FJZBCX, {
    params: {
      zbjs: keyWord,
    },
  });

export const getWFJBM = (guid: string) => {
  return request.get<unknown, Result<Array<GET_WFJBM_TYPE>>>(API.Get_WFJBM, {
    params: { guid },
  });
};
export const copyZBJB = (id: string, newname: string) =>
  request.post<unknown, Result<any>>(API.Copy_ZBJB + `?id=${id}&newname=${newname}`);

export const updateQyZBJB = (id: string, qyzt: string) =>
  request.post<unknown, Result<any>>(API.Update_QY_ZBJB + `?id=${id}&QYZT=${qyzt}`);

<template>
  <div v-if="reload">
    <el-row>
      <el-col :span="20">
        <div class="header">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="目标任务">
              <el-input
                v-model="formInline.zbrw"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="目标要求">
              <el-input
                v-model="formInline.mbyq"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="指标类型">
              <el-select v-model="formInline.zblx" placeholder="请选择">
                <el-option
                  v-for="item in zblxdata"
                  :key="item.guid"
                  :label="item.lxmc"
                  :value="item.lxmc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchData">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button>全部</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="center"></div>
        <div class="tabledata">
          <el-table
            :data="tableData"
            style="width: 100%"
            v-loading="loading"
            :row-key="(row: any) => row.guid"
            @row-click="handleRowClick"
            highlight-current-row
            :cell-style="columnStyle"
          >
            <el-table-column width="40px" align="center" fixed>
              <template #default="{ row }">
                <el-radio v-model="radio" :label="row.guid"></el-radio>
              </template>
            </el-table-column>
            <el-table-column
              property="xyzp"
              label="学院自评"
              width="110"
              align="center"
              :formatter="driverNameFormat"
            >
              <template #default="{ row }">
                <el-select
                  v-model="row.xyzp"
                  placeholder="请选择"
                  @visible-change="handleSelect(row)"
                  @change="selectValue(row)"
                  v-if="
                    row.spzt !== '已完成' &&
                    row.spzt !== '审批中' &&
                    row.sfbmtxxyjck == '否'
                  "
                >
                  <el-option
                    v-for="item in XYPJData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div v-if="row.spzt == '已完成' || row.spzt == '审批中'">
                  {{ row.xyzp }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="zbrw"
              label="目标任务"
              sortable
              show-overflow-tooltip
              min-width="150px"
            >
              <template #default="{ row }">
                <el-tag
                  class="tag1"
                  v-if="row.sfbmtxxyjck == '是' && row.zrlx == '自主填报'"
                  style="border: none"
                  size="small"
                >
                  部门填
                </el-tag>
                <el-tag
                  class="tag2"
                  v-if="
                    row.wcqk !== null &&
                    row.wcqk !== '' &&
                    row.sfbmtxxyjck == '否' &&
                    row.zrlx == '自主填报'
                  "
                  style="border: none"
                  size="small"
                >
                  已填报
                </el-tag>
                <el-tag
                  class="tag3"
                  v-if="row.zrlx == '系统集成'"
                  style="border: none"
                  size="small"
                >
                  系统抽
                </el-tag>
                <el-tag
                  class="tag3"
                  v-if="row.hosttaskid !== null"
                  style="border: none"
                  size="small"
                >
                  协办
                </el-tag>
                <span>{{ row.zbrw }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="mbyq"
              label="目标要求"
              sortable
              show-overflow-tooltip
            />
            <el-table-column
              prop="zblx"
              label="指标类型"
              sortable
              align="center"
              show-overflow-tooltip
              width="110"
            />
            <el-table-column
              prop="ssmk"
              label="所属模块"
              sortable
              align="center"
              width="130"
              show-overflow-tooltip
            />
            <el-table-column
              prop="xdbm"
              label="下达部门"
              sortable
              align="center"
              show-overflow-tooltip
              width="150"
            />
            <el-table-column
              prop="ssbm"
              label="责任部门"
              sortable
              align="center"
              width="110"
              show-overflow-tooltip
            />
          </el-table>
        </div>
        <div class="right-page">
          <el-pagination
            v-model:current-page="page"
            v-model:page-size="limit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="total"
            class="page-fen"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
      <el-col :span="4">
        <h2>目标完成情况</h2>
        <el-divider style="margin: 10px 0" />
        <div v-if="currentBCSM !== ''" class="left-header">
          <h3 style="text-align: center; margin-bottom: 5px">
            本部门已完成：{{ currentCount }}项
          </h3>
          <span>同步时间：{{ currentWCS }}</span>
          <div style="text-align: center">
            <router-link
              target="_blank"
              :to="{
                path: '/WCQK',
                query: { guid: rowInfo.guid, depName: rowInfo.ssbm },
              }"
              style="padding: 10px; color: #2a59b6; cursor: pointer"
            >
              查看完成情况
            </router-link>
          </div>
          <div class="bottom-plugin">
            <el-divider style="margin: 10px 0" />
            <h2>补充说明情况</h2>
            <el-divider style="margin: 10px 0" />
            <span>{{ delHtmlTag(currentBCSM) }}</span>
          </div>
        </div>
        <div class="connent" v-else v-if="isShow">
          <el-scrollbar>
            <span>修订时间：{{ currentWCS }}</span>
            <br />
            <span>修订次数：{{ currentRWCount }}</span>
            <br />
            <span>自评结果：{{ currentClickXyzp }}</span>
            <p>{{ currentWCQK }}</p>
            <div class="fujian">
              <h3 v-if="currentFJ !== null">附件：</h3>
              <div class="fujian-plugin">
                <a
                  @click="onFy(item.uri)"
                  style="cursor: pointer"
                  v-for="(item, index) of currentFJ"
                  :key="index"
                >
                  {{ item.name }}
                </a>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import {
  getCOLLEGETASK,
  getWCQKByRWId,
  getBCSMById,
  checkReportTask,
  bmtxSaveEvaluate,
  getCurrentUserDepName,
  getHQWCQK,
} from "@/api/TYJK/index";
import { getALLZBLX } from "@/api/MBXD/index";
import { getCurYear } from "@/utils";
import { reactive, ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useAuth } from "vue-oidc-provider";
import { XYPJSelect } from "@/utils/permissionConfig";
import { ElMessage, ElMessageBox } from "element-plus";
import axios from "axios";

const { user } = useAuth();
const route = useRoute();
const columnStyle = ({ columnIndex, row }: any) => {
  if (columnIndex == 1 && row.xyzp !== null) {
    return {
      color: "green",
    };
  }
  if (columnIndex == 1 && row.sfbmtxxyjck == "是" && row.zrlx == "自主填报") {
    return {
      color: "orange",
    };
  }
  if (columnIndex == 1 && row.xyzp == null) {
    return {
      color: "red",
    };
  }
};
const driverNameFormat = (row: any) => {
  return row.sfbmtxxyjck == "是" && row.zrlx == "自主填报"
    ? "部门填"
    : row.xyzp;
};

// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
const bcisShow = ref<boolean>(false);
const isShow = ref<boolean>(false);
const currentWCS = ref<string>("");
const currentWCQK = ref<string | null>("");
const currentRWCount = ref<number>(0);
const currentFJ = ref({
  item: {
    name: "",
    uri: "",
  },
});
const currentParams = ref();
const currentCount = ref();
const reload = ref(true);
const currentBCSM = ref<string | null>("");
const rowInfo = ref();
const radio = ref<boolean>(false);
const currentYear = ref<number>(0);
const loading = ref<boolean>(false);
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const selectSsbm = ref();
const zblxdata = ref<GET_ALLZBLX_Type[]>([]);
const formInline = reactive({
  mbyq: "",
  zbrw: "",
  zblx: "",
});
const XYPJData = ref<any>({
  label: "",
  value: "",
});
const currentClickXyzp = ref<string>("");
const checkPluginData = ref();
const tableData = ref<GET_ALLZNBMRW_Type[]>([]);

// 搜索
const searchData = async () => {
  try {
    const requestInfo = {
      zbrw: formInline.zbrw,
      mbyq: formInline.mbyq,
      zblx: formInline.zblx,
      ssbm: selectSsbm.value ? selectSsbm.value.ssbm : "",
    };
    loading.value = true;
    const res = await getCOLLEGETASK(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
      "2022",
    );
    total.value = res.count;
    tableData.value = res.data;
  } finally {
    loading.value = false;
  }
};

const getcurrentallbmrw = async () => {
  try {
    loading.value = true;
    const currentyear = route.query.year;
    const currentssbm = route.query.ssbm;
    const requestInfo = {
      zbrw: formInline.zbrw,
      mbyq: formInline.mbyq,
      zblx: formInline.zblx,
      ssbm: currentssbm,
    };
    const { data, count } = await getCOLLEGETASK(
      page.value,
      limit.value,
      JSON.stringify(requestInfo),
      currentyear,
    );
    total.value = count;
    tableData.value = data;
  } finally {
    loading.value = false;
  }
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  getcurrentallbmrw();
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  getcurrentallbmrw();
};

const getAllzblx = async () => {
  const { data } = await getALLZBLX();
  zblxdata.value = data;
};

// 点击表格某一行获取目标任务
const handleRowClick = async (row: any) => {
  radio.value = row.guid;
  rowInfo.value = row;
  currentBCSM.value = "";
  checkPluginData.value = [];
  currentClickXyzp.value = row.xyzp;
  if (row.zrlx == "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
    bcisShow.value = true;
  } else {
    const { data, count } = await getWCQKByRWId(row.guid);
    checkPluginData.value = data[0];
    console.log(checkPluginData.value);
    if (checkPluginData.value !== undefined) {
      isShow.value = true;
      currentWCS.value = data[0].wcsj.slice(0, 10);
      currentWCQK.value = data[0].wcqk;
      currentFJ.value = JSON.parse(data[0].fj);
      currentRWCount.value = count;
    }
    if (checkPluginData.value == undefined) {
      isShow.value = false;
    }
  }
};

const handleSelect = (row: any) => {
  XYPJSelect.forEach((item) => {
    if (row.zblx == item.zblx) {
      XYPJData.value = item.nzpj;
    }
  });
};

const selectValue = async (row: any) => {
  const res = await checkReportTask(row.guid);
  if (res === "未上报") {
    ElMessageBox.alert("评价失败，请先上报任务完成情况", "信息", {
      confirmButtonText: "确定",
    }).then(() => {
      row.xyzp = "";
    });
  } else {
    const { code } = await bmtxSaveEvaluate(row.guid, row.xyzp);
    if (code === 0) {
      ElMessage.success("编辑成功");
    }
  }
};

const onFy = (uri: string) => {
  const url = `https://ydmh.guangshaxy.com/formWebSite/form/${uri}`;
  window.open(url);
};

const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

onMounted(async () => {
  await getCurrentUserDep();
  getAllzblx();
  await getcurrentallbmrw();
  currentYear.value = await getCurYear();
});
</script>

<style lang="less" scoped>
h2 {
  text-align: center;
}
.left-header {
  text-align: center;
  span {
    color: #7a7a7a;
    font-weight: 400;
  }
  p {
    color: blue;
  }
}
.bottom-plugin {
  padding: 10px;
  margin-top: 200px;
}
h4 {
  padding: 10px;
  color: #2a59b6;
}
.connent {
  padding: 10px;
  height: calc(100vh - 600px);
  p {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  span {
    color: rgb(148, 148, 148);
  }
}

.scroll-container {
  width: 100%;
  overflow-x: scroll;
}

.scroll-content {
  display: flex;
  white-space: nowrap;
  height: calc(100vh - 180px); /* 设置容器高度 */
  overflow-y: auto; /* 设置垂直方向滚动条 */
}

/* 可以添加滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #c1c1c1;
}
.header {
  margin: 0px 10px;
  height: 45px;
  .el-form {
    display: flex;
  }
}
.center {
  display: flex;
  justify-content: space-between;
  text-align: center;
  margin: 0px 10px 0px 10px;
  .text {
    span {
      color: rgb(148, 148, 148);
      text-align: center;
    }
  }
}
.tabledata {
  margin: 12px 10px 10px 0px;
}
.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 95px);
}
.el-tag {
  background: #5a9cf8;
  color: #fff;
  margin-right: 5px;
}
.page-fen {
  margin-left: 150px;
}
.el-table {
  height: calc(100vh - 205px);
  border: 1px solid rgb(236, 238, 244);
}
.el-row {
  padding: 15px 20px 15px 20px;
}
/* 更改背景色 */
/deep/.el-popper {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}
/deep/ .el-radio__label {
  display: none;
}
.el-scrollbar {
  height: calc(100vh - 400px);
}
/deep/.el-dialog__body {
  padding: 0 20px 20px 20px;
}
.fujian {
  margin-top: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 200px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}
.tag1 {
  background: orange;
  color: #fff;
  margin-right: 5px;
}
.tag2 {
  background: green;
  color: #fff;
  margin-right: 5px;
}
.tag3 {
  background: #3896fb;
  color: #fff;
  margin-right: 5px;
}

.header-btn {
  text-align: center;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>

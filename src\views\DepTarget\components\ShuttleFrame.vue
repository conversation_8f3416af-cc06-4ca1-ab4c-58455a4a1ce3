<template>
  <div class="header-connent">
    <div class="left">
      <h2>{{ props.year === "更多" ? "全部" : props.year + "年" }}目标任务</h2>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="责任部门：">
          <el-select v-model="formInline.ssbm1" placeholder="请选择">
            <el-option
              v-for="item in ssbmdata"
              :key="item.departmentid"
              :label="item.departmentname"
              :value="item.departmentname"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目标任务：">
          <el-input v-model="formInline.zbrw" placeholder="请输入目标任务" />
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="searchData1">搜索</el-button>
      <el-button type="primary">全部</el-button>
    </div>
    <div class="right">
      <h2>{{ currentYear }}年目标任务</h2>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="责任部门：">
          <el-select v-model="formInline.ssbm2" placeholder="请选择">
            <el-option
              v-for="item in ssbmdata"
              :key="item.departmentid"
              :label="item.departmentname"
              :value="item.departmentname"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="目标任务：">
          <el-input v-model="formInline.zbrw" placeholder="请输入目标任务" />
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="searchData2">搜索</el-button>
      <el-button type="primary" @click="getAllRW">全部</el-button>
    </div>
  </div>
  <div class="main-connent">
    <el-row>
      <el-col :span="11">
        <el-table
          border
          ref="multipleTableRef"
          :data="leftTableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          v-loading="leftloading"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            property="nd"
            label="年度"
            width="120"
            align="center"
          ></el-table-column>
          <el-table-column
            property="ssbm"
            label="责任部门"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            property="zbrw"
            label="目标任务"
            show-overflow-tooltip
            align="center"
          />
        </el-table>
        <div class="right-page">
          <el-pagination
            v-model:current-page="leftpage"
            v-model:page-size="letflimit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            small
            :disabled="disabled"
            :background="background"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="lefttotal"
            @size-change="searchData1"
            @current-change="searchData1"
            class="page-fen"
          />
          <!-- <el-button class="footer-btn" size="small">确定</el-button> -->
        </div>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" class="center-btn" @click="moveToRight">
          >>
        </el-button>
      </el-col>
      <el-col :span="11">
        <!-- 右侧表格 -->
        <el-table
          border
          ref="multipleTableRef"
          :data="rightTableData"
          style="width: 100%"
          v-loading="rightableloading"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            property="nd"
            label="年度"
            width="120"
            align="center"
          ></el-table-column>
          <el-table-column
            property="ssbm"
            label="责任部门"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            property="zbrw"
            label="目标任务"
            show-overflow-tooltip
            align="center"
          />
        </el-table>
        <div class="right-page">
          <el-pagination
            v-model:current-page="rightpage"
            v-model:page-size="rightlimit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            small
            :disabled="disabled"
            :background="background"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="righttotal"
            @size-change="searchData2"
            @current-change="searchData2"
            class="page-fen"
          />
          <!-- <el-button class="footer-btn" size="small">确定</el-button> -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { getCurYear } from "@/utils";
import { ref, reactive, onMounted } from "vue";
import { getAllZNBMRW, addzb } from "@/api/TYJK/index";
import { getRwByYear, getALLZRBM } from "@/api/MBXD/index";
const props = defineProps(["year"]);
const currentYear = ref<number>(0);
const ssbmdata = ref<GET_AllZRBM_Type[]>([]);
const righttotal = ref<number>(0);
const rightpage = ref<number>(1);
const rightlimit = ref<number>(10);
const rightableloading = ref<boolean>(false);
const rightTableData = ref<GET_ALLZNBMRW_Type[]>([]);
const leftloading = ref<boolean>(false);
const leftpage = ref<number>(1);
const letflimit = ref<number>(10);
const lefttotal = ref<number>(0);
const leftTableData = ref<SEARCH_YEAR_Type[]>([]);
const multipleSelection = ref<GET_ALLZNBMRW_Type[]>([]);
const formInline = reactive({
  ssbm1: "",
  ssbm2: "",
  zbrw: "",
});
const background = ref(false);
const disabled = ref(false);

const getAllssbm = async () => {
  const { data } = await getALLZRBM();
  ssbmdata.value = data;
};
// 多选左侧数据
const handleSelectionChange = (val: GET_ALLZNBMRW_Type[]) => {
  multipleSelection.value = val;
};
// 将数据移到右侧
const moveToRight = () => {
  multipleSelection.value.forEach(async (item) => {
    await addzb(item.guid);
    rightTableData.value = multipleSelection.value;
  });
};

//左面搜索
const searchData1 = async () => {
  try {
    leftloading.value = true;
    const res = await getRwByYear(
      props.year,
      leftpage.value,
      letflimit.value,
      formInline.zbrw,
      formInline.ssbm1,
    );
    console.log(formInline.zbrw, formInline.ssbm1);

    righttotal.value = res.count;
    leftTableData.value = res.data;
  } finally {
    leftloading.value = false;
  }
};

// 当前年份搜索
const searchData2 = async () => {
  try {
    const requestInfo = {
      zbrw: formInline.zbrw,
      ssbm: formInline.ssbm2,
    };
    rightableloading.value = true;
    const res = await getAllZNBMRW(
      rightpage.value,
      rightlimit.value,
      JSON.stringify(requestInfo),
    );
    righttotal.value = res.count;
    rightTableData.value = res.data;
  } finally {
    rightableloading.value = false;
  }
};

// 获取到当前年份全部的任务
const getAllRW = async () => {
  try {
    rightableloading.value = true;
    const { data, count } = await getAllZNBMRW(
      (rightpage.value = 1),
      (rightlimit.value = 10),
    );
    rightTableData.value = data;
    righttotal.value = count;
  } finally {
    rightableloading.value = false;
  }
};

const getcurrentallbmrw = async () => {
  try {
    rightableloading.value = true;
    const { data, count } = await getAllZNBMRW(
      rightpage.value,
      rightlimit.value,
    );
    righttotal.value = count;
    rightTableData.value = data;
  } finally {
    rightableloading.value = false;
  }
};

// 获取左面年份所有的责任部门任务
const getrwbyyear = async () => {
  try {
    leftloading.value = true;
    const { data, count } = await getRwByYear(
      props.year,
      leftpage.value,
      letflimit.value,
    );
    leftTableData.value = data;
    lefttotal.value = count;
  } finally {
    leftloading.value = false;
  }
};

onMounted(async () => {
  getcurrentallbmrw();
  getAllssbm();
  getrwbyyear();
  currentYear.value = await getCurYear();
});
</script>

<style lang="less" scoped>
.header-connent {
  display: flex;
  justify-content: space-between;
  height: 124px;
  .left {
    width: 548px;
    height: 100%;
    h2 {
      padding: 0;
      margin-top: 0;
      margin-bottom: 10px;
    }
    .el-form {
      display: flex;
    }
  }
  .right {
    width: 548px;
    height: 100%;
    margin-left: 100px;
    margin-right: 85px;
    h2 {
      padding: 0;
      margin-top: 0;
      margin-bottom: 10px;
    }
    .el-form {
      display: flex;
    }
  }
}
.main-connent {
  width: 100%;
  // height: 520px;
  margin-top: 10px;
  .center-btn {
    margin-left: 23px;
    margin-top: 200px;
  }

  .right-page {
    display: flex;
    .page-fen {
      // width: 100px;
      margin-top: 10px;
    }
    .footer-btn {
      margin-top: 10px;
      margin-left: 20px;
    }
  }
}

/deep/.el-dialog__body {
  padding: 5px 10px 20px 10px;
}
</style>

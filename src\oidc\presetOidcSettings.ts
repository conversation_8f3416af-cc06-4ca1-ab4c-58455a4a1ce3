import { WebStorageStateStore } from "oidc-client-ts";

export const userStore = window.localStorage;

export const presetOidcSettings = {
  redirect_uri: location.origin + "/oidc-callback",
  popup_redirect_uri: location.origin + "/oidc-popup-callback",
  silent_redirect_uri: location.origin + "/silent-renew-oidc.html",
  loadUserInfo: true,
  userStore: new WebStorageStateStore({ store: userStore }),
};

import { createRouter, createWeb<PERSON>ashH<PERSON>ory, RouteRecordRaw } from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: "/home",
    component: () => import("@/views/LayOut/index.vue"),
    children: [
      {
        path: "/home",
        name: "home",
        component: () => import("@/views/Home/index.vue"),
      },
      {
        path: "/DepTarget",
        name: "DepTarget",
        component: () => import("@/views/DepTarget/index.vue"),
      },
      {
        path: "/YearEndTargetTask",
        name: "YearEndTargetTask",
        component: () => import("@/views/YearEndTargetTask/index.vue"),
      },
      {
        path: "/DepartmentCompletion",
        name: "DepartmentCompletion",
        component: () => import("@/views/DepartmentCompletion/index.vue"),
      },
      {
        path: "/AllCollegeTargetTask",
        name: "AllCollegeTargetTask",
        component: () => import("@/views/AllCollegeTargetTask/index.vue"),
      },
      {
        path: "/AssessmentResults",
        name: "AssessmentResults",
        component: () => import("@/views/AssessmentResults/index.vue"),
      },
      {
        path: "/FunctionalDepartment",
        name: "FunctionalDepartment",
        component: () => import("@/views/FunctionalDepartment/index.vue"),
      },
      {
        path: "/DeptartmentTargetTaskReport",
        name: "DeptartmentTargetTaskReport",
        component: () =>
          import("@/views/DeptartmentTargetTaskReport/index.vue"),
      },
      {
        path: "/ColllegeTargetTaskReport",
        name: "ColllegeTargetTaskReport",
        component: () => import("@/views/ColllegeTargetTaskReport/index.vue"),
      },
      {
        path: "/ColllegeTargetTaskReportCode",
        name: "ColllegeTargetTaskReportCode",
        component: () => import("@/views/ColllegeTargetTaskReport/index.vue"),
      },
      {
        path: "/ApprovalCenter",
        name: "ApprovalCenter",
        component: () => import("@/views/ApprovalCenter/index.vue"),
      },
      {
        path: "/Kpiofdepartment",
        name: "Kpiofdepartment",
        component: () => import("@/views/Kpiofdepartment/index.vue"),
      },

      {
        path: "/KpiOfCollege",
        name: "KpiOfCollege",
        component: () => import("@/views/KpiOfCollege/index.vue"),
      },
      {
        path: "/SystemManagement",
        name: "SystemManagement",
        component: () => import("@/views/SystemManagement/index.vue"),
      },
      {
        path: "/CollegeTaskManagement",
        name: "CollegeTaskManagement",
        component: () => import("@/views/CollegeTaskManagement/index.vue"),
      },
      {
        path: "/DeptartmentTargetTaskReportAll",
        name: "DeptartmentTargetTaskReportAll",
        component: () =>
          import("@/views/DeptartmentTargetTaskReportAll/index.vue"),
      },
      {
        path: "/DepartmentTargetTaskReportCollege",
        name: "DepartmentTargetTaskReportCollege",
        component: () =>
          import(
            "@/views/DeptartmentTargetTaskReportAll/DepartmentTargetTaskReportCollege.vue"
          ),
      },
      {
        path: "/OperatingManual",
        name: "OperatingManual",
        component: () => import("@/views/OperatingManual/index.vue"),
      },
      {
        path: "/LevelMaintenance",
        name: "LevelMaintenance",
        component: () => import("@/views/LevelMaintenance/index.vue"),
        children: [
          {
            path: "/LevelMaintenance/:id",
            name: "LevelMaintenanceId",
            component: () =>
              import(
                "../views/LevelMaintenance/components/OrganizationMain.vue"
              ),
          },
        ],
      },
      {
        path: "/StatisticsWarning",
        name: "StatisticsWarning",
        component: () => import("@/views/StatisticsWarning/Index.vue"),
      },
    ],
  },
  {
    path: "/WCQK",
    name: "/WCQK",
    component: () => import("@/views/WCQK/index.vue"),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;

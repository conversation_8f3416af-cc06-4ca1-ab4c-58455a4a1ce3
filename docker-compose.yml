version: "3.4"

services:
  target-manager:
    image: target-manager
    container_name: target-manager
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 8808:80
    restart: always
    network_mode: bridge
    volumes:
      - ./dist:/usr/share/nginx/html/
      # http
      - ./nginx.conf:/etc/nginx/nginx.conf
      # or
      # https SSL
      # - ./nginx.ssl.conf:/etc/nginx/nginx.conf
      # - ./ssl/:/etc/nginx/ssl/

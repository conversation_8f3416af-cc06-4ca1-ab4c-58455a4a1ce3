interface GET_BMMB_Type {
  [x: string]: string;
  name: string;
  bmid: string;
  ssbm: string;
  datatype: string;
  px: number;
  nd: string;
}

interface Result<T> {
  code: number;
  count: number;
  data: T;
  msg: string;
}

type GetCollegeCompletionDetailByTaskIdResult = {
  guid: string;
  nd: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  bz: string;
  sjrw: string;
  ck: null;
  zrlx: string;
  xtpj: null;
  xyzp: null;
  xyzpfbzt: null;
  nzpj: null;
  nzpjfbzt: null;
  bmgxypj: null;
  bmgxypjfbzt: null;
  bcsm: null;
  ssbmpx: number;
  bmid: string;
  ssbm: string;
  zbly: string;
  znbmtjr: null;
  fzghcshr: null;
  dqzt: string;
  fK_GUID: null;
  hxxzb: string;
  createtime: null;
  hosttaskid: null;
  fzrgh: string;
  fzrxm: string;
  sfwc: null;
  datatype: string;
  wcqk: string;
  wcsj: null;
  fj: null;
  taskid: null;
  xdbm: string;
  oldguid: null;
  rwpx: null;
  xbbm: null;
  bmrwfzrxm: string;
  ssmk: string;
  sfbmtxxyjck: string;
  zpsbincident: null;
  bmssmbzrlx: null;
  sflhzb: string;
  lhzbgz: string;
  row_num: null;
  spzt: null;
  spztlj: null;
  sjzb: string;
  ejzb: string;
  yjzb: string;
  sjzbfz: number;
  ejzbfz: number;
  yjzbfz: number;
  zbrwfz: number;
  zbshzt: string;
  sfyc: null;
  jd: null;
};

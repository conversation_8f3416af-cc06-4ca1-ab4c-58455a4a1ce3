<template>
  <div class="department-main">
    <div class="bm-header">
      <el-icon><OfficeBuilding /></el-icon>
      <h3>{{ mc }}</h3>
      <el-button @click="editDept">编辑</el-button>
    </div>
    <div class="bm-center">
      <!-- 下级部门 -->
      <div class="left">
        <DepartmentSub :deptId="route.params.id" />
      </div>
    </div>
  </div>
  <el-drawer v-model="drawer" :direction="direction">
    <template #header>
      <h4 style="font-weight: 300; margin: 0">编辑机构信息</h4>
    </template>
    <template #default>
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="机构名称" prop="mc">
          <el-input v-model="ruleForm.mc" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item style="margin-left: 80px">
          <el-button type="primary" @click="saveEidtOrg">保存</el-button>
          <el-button type="danger" plain @click="deleteDept">删除</el-button>
        </el-form-item>
      </el-form>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { getZBJBXX, updateZBJB, Delete_ZBJB } from "../../../api/TYJK/index";
import type { Get_ZBJB_type } from "../../../api/TYJK/type";
import DepartmentSub from "./DepartmentSub.vue";
import router from "../../../router";
import { OfficeBuilding } from "@element-plus/icons-vue";
import {
  ElInput,
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
} from "element-plus";
import { reactive, ref, watch } from "vue";
import { useAuth } from "vue-oidc-provider";
import { useRoute } from "vue-router";

// const { user } = useAuth()
const emit = defineEmits(["eventHandler"]);
const route = useRoute();
const drawer = ref(false);
const direction = ref("rtl");
interface RuleForm {
  sjmc: string;
  mc: string;
}
const formSize = ref("default");
const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
  sjmc: "",
  mc: "",
});

const rules = reactive<FormRules<RuleForm>>({
  sjmc: [
    { required: true, message: "名称不可为空", trigger: "blur" },
    { min: 1, max: 50, message: "名称不可为空", trigger: "blur" },
  ],
  mc: [
    { required: true, message: "名称不可为空", trigger: "blur" },
    { min: 1, max: 50, message: "名称不可为空", trigger: "blur" },
  ],
});

const orgInfoData = ref<Get_ZBJB_type[]>([]);
const mc = ref("");

const getorginfpmation = async () => {
  const id = route.params.id?.toString();
  const { data } = await getZBJBXX(id);
  orgInfoData.value = data;
  mc.value = data[0].mc;
  console.log(orgInfoData.value);
};

const saveEidtOrg = async () => {
  const valid = await ruleFormRef.value?.validate();
  if (valid) {
    await updateZBJB(route.params.id as string, ruleForm.mc);
    drawer.value = false;
    getorginfpmation();
    ElMessage.success("修改成功");
  }
};

const editDept = () => {
  const { mc } = orgInfoData.value[0];
  ruleForm.mc = mc;
  drawer.value = true;
};

const deleteDept = async () => {
  ElMessageBox.confirm("确定删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await Delete_ZBJB(route.params.id as string);
    drawer.value = false;
    ElMessage.success("删除成功");
    router.replace({
      name: "LevelMaintenanceId",
      params: {
        id: route.query.parentId as string,
      },
    });
    getorginfpmation();
  });
};

watch(
  () => route.params.id,
  (newValue, oldValue) => {
    if (newValue) {
      getorginfpmation();
    }
  },
  { immediate: true },
);
</script>

<style lang="less" scoped>
.department-main {
  padding: 20px;
  .bm-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    h3 {
      font-size: 20px;
      font-weight: 400;
      margin: 0;
      margin-left: 8px;
      margin-right: 8px;
    }
    .el-icon {
      font-size: 25px;
    }
  }
}
:deep(.el-drawer__body) {
  margin-top: 0;
}

.bm-center {
  display: flex;
  justify-content: space-between;
  width: 100%;
  .left {
    width: 100%;
  }
}
</style>

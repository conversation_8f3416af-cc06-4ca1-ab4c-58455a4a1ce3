import { createApp } from "vue";
import App from "./App.vue";
import ElementPlus from "element-plus";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import "element-plus/dist/index.css";

import "normalize.css";
import "@/assets/styles/common.less";

import router from "./router";

import { useScriptTag } from "@vueuse/core";

useScriptTag(`/config.js?t=${Date.now()}`, async () => {
  const app = createApp(App);
  app.use(router);
  app.mount("#app");
  app.use(ElementPlus, {
    locale: zhCn,
  });
});

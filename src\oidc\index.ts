import { userGetterGen } from "vue-oidc-provider";
import { presetOidcSettings, userStore } from "./presetOidcSettings";

export interface Job {
  CompanyId: string;
  CompanyName: string;
  DepartmentEnName: string;
  DepartmentId: string;
  DepartmentName: string;
  Id: string;
  IsManager: boolean;
  IsPrimary: boolean;
  JobName: string;
  OrgPath: string;
  RefJobId: string;
}

export interface OidcUser {
  Avatar: string;
  Domain: string;
  Email: string;
  Id: string;
  Jobs: Job | Job[];
  Language: string;
  PhoneNumber: string | number;
  RoleIds: string;
  UserAccount: string;
  UserEnName: string;
  UserId: string;
  UserName: string;
  UserRoles: { Id: string; Name: string }[] | { Id: string; Name: string };
  amr: string[];
  auth_time: number | string;
  idp: string;
  preferred_username: string;
  role: string[];
  sid: string;
  sub: string;
}

export type idpType = `idp:${"Weixin" | "DingTalk" | "Platform"}` | null;

declare module "oidc-client-ts" {
  interface IdTokenClaims extends OidcUser {}
}

const { authority, client_id } = window.projectConfig.oidcSettings;

export const useUser = userGetterGen(authority, client_id, userStore);

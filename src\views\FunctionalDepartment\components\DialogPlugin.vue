<template>
  <el-row>
    <el-col :span="4">
      <h2>本部门目标任务</h2>
      <div class="search">
        <p>考核内容及标准：</p>
        <el-input
          v-model="searchValue"
          @input="handleSearch"
          style="width: 100%"
          clearable
          placeholder="请输入关键字"
          :suffix-icon="Search"
        />
      </div>
      <el-divider style="margin: 10px 0" />
      <!--      <el-scrollbar>-->
      <!--        <ul class="connent">-->
      <!--          <li-->
      <!--            @click="clickCurrentFJRW(item, index)"-->
      <!--            v-for="(item, index) in ZBRWData"-->
      <!--            :key="index"-->
      <!--            :class="{ activeCss: activeVar == index }"-->
      <!--          >-->
      <!--            <el-tag-->
      <!--              v-show="item.explodeState"-->
      <!--              :style="getTagStyle(item.explodeState)"-->
      <!--              style="border: none"-->
      <!--              size="small"-->
      <!--            >-->
      <!--              {{ getTagConnent(item.explodeState) }}-->
      <!--            </el-tag>-->
      <!--            {{ item.zbrw }}-->
      <!--          </li>-->
      <!--        </ul>-->
      <!--      </el-scrollbar>-->
      <el-scrollbar height="calc(100% - 70px)">
        <ul class="connent">
          <li
            @click="clickCurrentFJRW(item, index)"
            v-for="(item, index) in ZBRWData"
            :key="index"
            :class="{ activeCss: activeVar == index }"
          >
            <el-tooltip class="item-tooltip" effect="light" placement="top">
              <template #content>
                <div style="max-width: 300px">
                  {{ item.zbrw }}
                </div>
              </template>
              <div
                @mouseover="showTooltip = true"
                @mouseout="showTooltip = false"
                :style="{ cursor: 'pointer' }"
              >
                <el-tag
                  v-show="item.explodeState"
                  :style="getTagStyle(item.explodeState)"
                  style="border: none"
                  size="small"
                >
                  {{ getTagConnent(item.explodeState) }}
                </el-tag>
                {{ item.zbrw }}
              </div>
            </el-tooltip>
          </li>
        </ul>
      </el-scrollbar>
    </el-col>
    <el-col :span="20">
      <div class="right">
        <div class="right-top">
          <el-button type="primary" @click="handleFilter('all')">
            全部
          </el-button>
          <el-button type="primary" @click="handleFilter('department')">
            下达部门评分的指标
          </el-button>
          <el-button type="primary" @click="handleFilter('college')">
            责任部门自评的指标
          </el-button>
        </div>
        <el-button type="primary" @click="oneClickAdd">分解全部任务</el-button>
        <el-button type="primary" @click="oneClickAddDepart">
          分解至部门
        </el-button>
        <el-button type="primary" @click="oneClickAddCollege">
          分解至学院
        </el-button>
        <el-button type="primary" @click="addRW">+单独分解</el-button>
        <el-button type="primary" @click="saveRWData">暂存</el-button>
        <el-button type="primary" @click="confirmDecomposition">
          确认分解
        </el-button>
        <el-button type="danger" @click="deleteBatchRW">批量删除</el-button>

        <span>“确认分解”之后才算任务分解成功!</span>
        <div class="right-two" v-if="clickObject.sflhzb == '是'">
          <h2>指标考核规则设置</h2>
          <p>指标任务：{{ clickObject.zbrw }}</p>
          <p>指标类型：{{ clickObject.zblx }}</p>
          <p>目标要求：{{ clickObject.mbyq }}</p>
          <div class="zbgzTabledata">
            <el-table
              ref="multipleTableRef"
              :data="zbgzTabledata"
              style="width: 100%"
              @selection-change="zbgzHandleSelectionChange"
              v-loading="zbkhloading"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column
                property="ssbm"
                label="学院"
                show-overflow-tooltip
                min-width="150px"
                align="center"
              />
              <el-table-column
                v-for="(item, index) in lhzbArr"
                :key="index"
                :label="item"
                width="200"
                align="center"
                property="selectValue"
              >
                <template #default="{ row }">
                  <div class="parent">
                    <el-select
                      v-model="row['selectValue' + index]"
                      placeholder="请选择"
                      class="son1"
                      @change="onSelectValue(row)"
                    >
                      <el-option
                        v-for="item in options"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                    <el-input
                      v-model="row['mb' + index]"
                      class="son2"
                      @blur="onSelectValue(row)"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                width="100"
                align="center"
                fixed="right"
              >
                <template #default="{ row }">
                  <el-button
                    size="small"
                    type="danger"
                    @click="deletelhRW(row.$index, row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="right-one" v-else>
          <div class="right-btn"></div>
          <div class="tabledata">
            <el-table
              ref="multipleTableRef"
              :data="tableData"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              v-loading="loading"
            >
              <el-table-column type="selection" width="55" />

              <el-table-column label="分解状态" width="100">
                <template #default="{ row }">
                  <el-tag
                    v-show="row.explodeState"
                    :style="getTagZRWStyle(row.explodeState)"
                    style="border: none"
                    size="small"
                  >
                    {{ getTagZRWConnent(row.explodeState) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                property="zbrw"
                label="考核内容及标准"
                width="250"
                show-overflow-tooltip
              />
              <el-table-column
                property="mbyq"
                label="评分办法"
                width="250"
                show-overflow-tooltip
              />
              <el-table-column
                property="zblx"
                label="指标类型"
                width="120"
                align="center"
                show-overflow-tooltip
              />
              <el-table-column
                property="ssbm"
                label="责任部门"
                width="150"
                align="center"
                show-overflow-tooltip
              />
              <el-table-column
                label="操作"
                width="150"
                align="center"
                fixed="right"
              >
                <template #default="scope">
                  <el-button
                    size="small"
                    type="primary"
                    @click="edit(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="deleteRW(scope.$index, scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <!-- 量化指标新增 -->
  <el-dialog
    v-model="addLhzbDialogVisible"
    title="添加学院"
    width="500px"
    destroy-on-close
    :close-on-click-modal="false"
    :before-close="lhzbHandleClose"
  >
    <el-form :model="addForm" label-width="100px" :rules="rules">
      <el-form-item label="责任部门：" prop="ssbm">
        <el-select v-model="addForm.ssbm" placeholder="请选择" filterable>
          <el-option
            v-for="item in collegeInfoData"
            :key="item.departmentid"
            :label="item.departmentname"
            :value="item.departmentname"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelLhzb">取消</el-button>
        <el-button type="primary" @click="addLhzbSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 添加弹框 -->
  <el-dialog
    v-model="addDialogVisible"
    title="添加学院"
    width="500px"
    destroy-on-close
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form :model="form" label-width="100px" :rules="rules">
      <el-form-item label="责任部门：" prop="ssbm">
        <el-select v-model="form.ssbm" placeholder="请选择" filterable>
          <el-option
            v-for="item in departmentList"
            :key="item.guid"
            :label="item.bmmc"
            :value="item.guid"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="目标要求：" prop="mbyq">
        <el-input
          v-model="form.mbyq"
          type="textarea"
          placeholder="请输入"
          :autosize="{ minRows: 5, maxRows: 8 }"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="addSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 编辑弹框 -->
  <el-dialog
    v-model="editDialogVisible"
    title="信息"
    width="500px"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <p style="margin-top: 20px">评分办法：</p>
    <el-input
      v-model="MBYQData.mbyq"
      type="textarea"
      placeholder="请输入"
      :autosize="{ minRows: 5, maxRows: 8 }"
    />
    <p style="margin-top: 20px">考核内容及标准：</p>
    <el-input
      v-model="MBYQData.zbrw"
      placeholder="请输入"
      type="textarea"
      :autosize="{ minRows: 5, maxRows: 8 }"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="editSubmit(MBYQData)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  getCurrentYearAllRWByDepartmentName,
  getRWFJZTById,
  getZRWFJZTById,
  getAllCollegeInfo,
  taskDecomposition,
  getAllSubtaskByTaskId,
  saveDecomposition,
  getZBLX,
  getAllBM,
  getAllXY,
  GetFJZBCX,
  getWFJBM,
} from "@/api/TYJK/index";
import { ref, onMounted } from "vue";
import { ElMessageBox, ElMessage, rowContextKey } from "element-plus";
import { Search } from "@element-plus/icons-vue";
const options = ["大于", "大于等于", "小于"];
const zbgzTabledata = ref<GET_RWFJZT_Type[]>([]);
const form = ref<Form>({
  ssbm: "",
  mbyq: "",
});
interface Form {
  mbyq: string;
  ssbm: string;
}
interface addForm {
  ssbm: string;
}
const addForm = ref<addForm>({
  ssbm: "",
});
const sbs = ref(0);
let sabs = ref(0);
const zbkhloading = ref<boolean>(false);
const loading = ref<boolean>(false);
const activeVar = ref();
const getTagZRWStyle = (type: string) => {
  if (type === "当前子任务已分解！") {
    return {
      background: "green",
      color: "#fff",
    };
  } else {
    return {
      background: "#ec6337",
      color: "#fff",
    };
  }
};
const getTagZRWConnent = (type: string) => {
  if (type == "当前子任务已分解！") {
    return "已分解";
  } else {
    return "未分解";
  }
};
const getTagStyle = (type: string) => {
  if (type === "当前任务已分解！") {
    return {
      background: "green",
      color: "#fff",
    };
  } else {
    return {
      background: "#ec6337",
      color: "#fff",
    };
  }
};
const getTagConnent = (type: string) => {
  if (type == "当前任务已分解！") {
    return "已分解";
  } else {
    return "未分解";
  }
};

// 字段校验
const rules = {
  ssbm: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
  mbyq: [{ required: true, message: "请输入不可为空喔", trigger: "blur" }],
};
const isOneAdd = ref<boolean>(false);
const oldTableData = ref();
const oldLhzbTableData = ref([]);
const addLhzbDialogVisible = ref<boolean>(false);
const collegeInfoData = ref<GET_COLLEGEINFO_Type[]>([]);
const departmentList = ref<Array<GET_WFJBM_TYPE>>([]);
const addDialogVisible = ref<boolean>(false);
const editDialogVisible = ref<boolean>(false);
const tableData = ref<GET_RWFJZT_Type[]>([]);
const ZBRWData = ref<GET_RWFJZT_Type[]>([]);
const multipleSelection = ref<GET_RWFJZT_Type[]>([]);
interface MBYQData {
  guid: string;
  mbyq: string;
  zbrw: string;
  ck: string;
  sflhzb: string;
}
const MBYQData = ref<MBYQData>({
  guid: "",
  mbyq: "",
  zbrw: "",
  ck: "",
  sflhzb: "",
});
const explodeState = ref<string>("");
interface clickObject {
  zbrw: string;
  zblx: string;
  mbyq: string;
  guid: string;
  explodeState: string;
  ssbm: string;
  sjrw: string;
  sflhzb: string;
  lhzbgz: string;
}
const clickObject = ref<clickObject>({
  zbrw: "",
  zblx: "",
  mbyq: "",
  guid: "",
  explodeState: "",
  sflhzb: "",
  ssbm: "",
  sjrw: "",
  lhzbgz: "",
});
const zbgzMultipleSelection = ref<POST_ADDLHZB_Type[]>([]);
const currentFJRW = ref<{ item: any; index: number }>({ item: {}, index: 0 });
const searchValue = ref<string>("");

const zbgzHandleSelectionChange = (val: POST_ADDLHZB_Type[]) => {
  zbgzMultipleSelection.value = val;
};

const handleSelectionChange = (val: GET_RWFJZT_Type[]) => {
  multipleSelection.value = val;
};

// 一键添加
const oneClickAdd = async () => {
  isOneAdd.value = true;
  oldTableData.value = tableData.value;
  oldLhzbTableData.value = zbgzTabledata.value;
  if (clickObject.value.sflhzb == "是") {
    // 获取动态表格表头
    const res = await getZBLX(clickObject.value.zblx);
    lhzbArr.value = res.data;
    const { data } = await getAllCollegeInfo();
    zbgzTabledata.value = [];
    data.forEach((item) => {
      let temp = {
        ssbm: item.departmentname,
        explodeState: "当前子任务未分解！",
        sjrw: clickObject.value.guid,
        zbrw: clickObject.value.zbrw,
        mbyq: clickObject.value.mbyq,
        zblx: clickObject.value.zblx,
        sflhzb: clickObject.value.sflhzb,
        guid:
          "lsl-" + Date.now() + "-" + Math.floor(Math.random() * 100000) + 1000,
      };
      zbgzTabledata.value.push(temp);
    });
    zbgzTabledata.value.map((ele) => {
      for (let i = 0; i < res.data.length; i++) {
        let name = "selectValue" + i;
        ele[name] = "大于等于";
      }
      return ele;
    });
  } else {
    if (explodeState.value === "") {
      ElMessageBox.alert("请先选择目标任务", "信息", {
        confirmButtonText: "确定",
      });
    } else {
      const { data } = await getAllCollegeInfo();
      tableData.value = [];
      data.forEach((item) => {
        let temp = {
          ssbm: item.departmentname,
          explodeState: "当前子任务未分解！",
          zbrw: clickObject.value.zbrw,
          mbyq: clickObject.value.mbyq,
          zblx: clickObject.value.zblx,
          sflhzb: clickObject.value.sflhzb,
          guid:
            "lsl-" +
            Date.now() +
            "-" +
            Math.floor(Math.random() * 100000) +
            1000,
          sjrw: clickObject.value.guid,
        };
        tableData.value.push(temp);
      });
    }
  }
};

// 一键添加部门
const oneClickAddDepart = async () => {
  isOneAdd.value = true;
  oldTableData.value = tableData.value;
  oldLhzbTableData.value = zbgzTabledata.value;
  if (clickObject.value.sflhzb == "是") {
    // 获取动态表格表头
    const res = await getZBLX(clickObject.value.zblx);
    lhzbArr.value = res.data;
    const { data } = await getAllBM();
    zbgzTabledata.value = [];
    data.forEach((item) => {
      let temp = {
        ssbm: item.bmmc,
        explodeState: "当前子任务未分解！",
        sjrw: clickObject.value.guid,
        zbrw: clickObject.value.zbrw,
        mbyq: clickObject.value.mbyq,
        zblx: clickObject.value.zblx,
        sflhzb: clickObject.value.sflhzb,
        guid:
          "lsl-" + Date.now() + "-" + Math.floor(Math.random() * 100000) + 1000,
      };
      zbgzTabledata.value.push(temp);
    });
    zbgzTabledata.value.map((ele) => {
      for (let i = 0; i < res.data.length; i++) {
        let name = "selectValue" + i;
        ele[name] = "大于等于";
      }
      return ele;
    });
  } else {
    if (explodeState.value === "") {
      ElMessageBox.alert("请先选择目标任务", "信息", {
        confirmButtonText: "确定",
      });
    } else {
      const { data } = await getAllBM();
      tableData.value = [];
      data.forEach((item) => {
        let temp = {
          ssbm: item.bmmc,
          explodeState: "当前子任务未分解！",
          zbrw: clickObject.value.zbrw,
          mbyq: clickObject.value.mbyq,
          zblx: clickObject.value.zblx,
          sflhzb: clickObject.value.sflhzb,
          guid:
            "lsl-" +
            Date.now() +
            "-" +
            Math.floor(Math.random() * 100000) +
            1000,
          sjrw: clickObject.value.guid,
        };
        tableData.value.push(temp);
      });
    }
  }
};
// 一键添加学院
const oneClickAddCollege = async () => {
  isOneAdd.value = true;
  oldTableData.value = tableData.value;
  oldLhzbTableData.value = zbgzTabledata.value;
  if (clickObject.value.sflhzb == "是") {
    // 获取动态表格表头
    const res = await getZBLX(clickObject.value.zblx);
    lhzbArr.value = res.data;
    const { data } = await getAllXY();
    zbgzTabledata.value = [];
    data.forEach((item) => {
      let temp = {
        ssbm: item.bmmc,
        explodeState: "当前子任务未分解！",
        sjrw: clickObject.value.guid,
        zbrw: clickObject.value.zbrw,
        mbyq: clickObject.value.mbyq,
        zblx: clickObject.value.zblx,
        sflhzb: clickObject.value.sflhzb,
        guid:
          "lsl-" + Date.now() + "-" + Math.floor(Math.random() * 100000) + 1000,
      };
      zbgzTabledata.value.push(temp);
    });
    zbgzTabledata.value.map((ele) => {
      for (let i = 0; i < res.data.length; i++) {
        let name = "selectValue" + i;
        ele[name] = "大于等于";
      }
      return ele;
    });
  } else {
    if (explodeState.value === "") {
      ElMessageBox.alert("请先选择目标任务", "信息", {
        confirmButtonText: "确定",
      });
    } else {
      const { data } = await getAllXY();
      tableData.value = [];
      data.forEach((item) => {
        let temp = {
          ssbm: item.bmmc,
          explodeState: "当前子任务未分解！",
          zbrw: clickObject.value.zbrw,
          mbyq: clickObject.value.mbyq,
          zblx: clickObject.value.zblx,
          sflhzb: clickObject.value.sflhzb,
          guid:
            "lsl-" +
            Date.now() +
            "-" +
            Math.floor(Math.random() * 100000) +
            1000,
          sjrw: clickObject.value.guid,
        };
        tableData.value.push(temp);
      });
    }
  }
};
const lhzbHandleClose = () => {
  addLhzbDialogVisible.value = false;
  addForm.value = {
    ssbm: "",
  };
};
const handleClose = () => {
  addDialogVisible.value = false;
  form.value = {
    mbyq: "",
    ssbm: "",
  };
};

// 不明用意，删除时没有明显影响
const getCollegeInfo = async () => {
  const { data } = await getAllCollegeInfo();
  collegeInfoData.value = data;
};
const lhzbArr = ref([]);

// 点击左侧任务
const clickCurrentFJRW = async (item: any, index: number) => {
  currentFJRW.value = { item, index };

  isOneAdd.value = false;
  // 选中内容高亮
  activeVar.value = index;
  clickObject.value = item;
  if (item.sflhzb === "是") {
    try {
      zbkhloading.value = true;
      // 获取动态表格表头
      const res = await getZBLX(item.zblx);
      lhzbArr.value = res.data;
      zbgzTabledata.value = [];
      const { data } = await getAllSubtaskByTaskId(item.guid);
      if (data !== null) {
        data.forEach((element) => {
          let temp = {
            ssbm: element.ssbm,
            sjrw: element.sjrw,
            guid: element.guid,
            mbyq: element.mbyq,
            sflhzb: element.sflhzb,
            zbrw: element.zbrw,
          };
          if (JSON.parse(element.lhzbgz) !== null) {
            let lhzbgz = JSON.parse(element.lhzbgz);
            for (let i = 0; i < lhzbgz.length; i++) {
              temp["selectValue" + i] = lhzbgz[i].gz;
              temp["mb" + i] = lhzbgz[i].mb;
            }
          }
          zbgzTabledata.value.push(temp);
        });
      }
      oldLhzbTableData.value = JSON.parse(JSON.stringify(zbgzTabledata.value));
    } finally {
      zbkhloading.value = false;
    }
  } else {
    try {
      tableData.value = [];
      explodeState.value = item.explodeState;
      loading.value = true;
      // 获取主任务
      const { data } = await getAllSubtaskByTaskId(item.guid);
      if (data !== null) {
        for await (const item of data) {
          // 获取子任务
          const res = await getZRWFJZTById(item.guid);
          item.explodeState = res.msg;
        }
      }
      tableData.value = data;
    } finally {
      loading.value = false;
    }
  }
};

const getCurrentYearAllByDepartmentName = async () => {
  const { data } = await getCurrentYearAllRWByDepartmentName();
  ZBRWData.value = data;
  getExplodeState();
};

const getExplodeState = async () => {
  for await (const item of ZBRWData.value) {
    const res = await getRWFJZTById(item.guid);
    item.explodeState = res.msg;
  }
};

const getDepartmentList = async () => {
  const { data } = await getWFJBM(currentFJRW.value.item.guid);
  departmentList.value = data;
};

// 添加
const addRW = async () => {
  //获取单独分解时的责任部门选项
  getDepartmentList();

  if (clickObject.value.mbyq === "") {
    ElMessageBox.alert("请先选择目标任务", "信息", {
      confirmButtonText: "确定",
    });
  } else {
    if (clickObject.value.sflhzb == "是") {
      addLhzbDialogVisible.value = true;
    } else {
      form.value.mbyq = clickObject.value.mbyq;
      addDialogVisible.value = true;
    }
  }
};

// 基本类型添加确定
const addSubmit = async () => {
  if (sabs.value === 0) {
    oldTableData.value = JSON.parse(JSON.stringify(tableData.value));
  }
  sabs.value = sabs.value + 1;
  // 若有数据
  if (tableData.value !== null) {
    let temp = {
      ssbm: form.value.ssbm,
      explodeState: "当前子任务未分解！",
      zbrw: clickObject.value.zbrw,
      mbyq: form.value.mbyq,
      zblx: clickObject.value.zblx,
      sjrw: clickObject.value.guid,
      sflhzb: clickObject.value.sflhzb,
      ck: "add",
    };
    // multipleSelection.value.push(temp);
    // tableData.value.push(temp);
    await saveDecomposition([temp]);
    await clickCurrentFJRW(clickObject.value, activeVar.value);
    addDialogVisible.value = false;
    form.value.ssbm = "";
  } else {
    // 若无数据
    let temp = [
      {
        ssbm: form.value.ssbm,
        explodeState: "当前子任务未分解！",
        zbrw: clickObject.value.zbrw,
        mbyq: form.value.mbyq,
        zblx: clickObject.value.zblx,
        sjrw: clickObject.value.guid,
        sflhzb: clickObject.value.sflhzb,
        ck: "add",
      },
    ];
    // tableData.value = temp;
    // multipleSelection.value.push(...temp);
    await saveDecomposition(temp);
    await clickCurrentFJRW(clickObject.value, activeVar.value);
    addDialogVisible.value = false;
  }
};

const edit = (row: any) => {
  editDialogVisible.value = true;
  MBYQData.value.mbyq = row.mbyq;
  MBYQData.value.guid = row.guid;
  MBYQData.value.zbrw = row.zbrw;
  MBYQData.value.sflhzb = row.sflhzb;
};

const editSubmit = async (MBYQData: any) => {
  tableData.value.forEach((item) => {
    if (item.guid === MBYQData.guid) {
      item.mbyq = MBYQData.mbyq;
      item.zbrw = MBYQData.zbrw;
    }
  });
  // 非一键添加的编辑
  if (isOneAdd.value == false) {
    const rrrr = [MBYQData];
    rrrr.forEach((ele) => {
      ele.ck = "edit";
    });
    // 添加虚拟数据不掉用接口
    if (rrrr[0].guid === undefined) {
      editDialogVisible.value = false;
      return;
    }
    const { code } = await saveDecomposition([...rrrr]);
    if (code === 0) {
      ElMessage.success("编辑成功");
    }
    editDialogVisible.value = false;
  }
};

// 批量删除
const deleteBatchRW = async () => {
  ElMessageBox.confirm("确定删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async (confirmed) => {
      if (!confirmed) return; // 用户点击了取消

      // for (const element of multipleSelection.value) {
      //   MBYQData.value.guid = element.guid;
      //   MBYQData.value.zbrw = element.zbrw;
      //   MBYQData.value.mbyq = element.mbyq;

      //   if (isOneAdd.value === false) {
      //     const itemToDelete = { ...MBYQData.value, ck: "delete" };
      //     // 假设 saveDecomposition 返回一个 Promise
      //     await saveDecomposition([itemToDelete]);

      //     // 重新获取表格数据
      //     await clickCurrentFJRW(
      //       currentFJRW.value.item,
      //       currentFJRW.value.index,
      //     );
      //   } else {
      //     // console.log(multipleSelection.value.indexOf(element))
      //     // tableData.value.splice(multipleSelection.value.indexOf(element), 1); // 注意这里需要找到正确的索引
      //     const index = tableData.value.findIndex(
      //       (item) => item.guid === element.guid,
      //     ); // 假设 guid 是唯一标识符
      //     if (index !== -1) {
      //       tableData.value.splice(index, 1);
      //     }
      //   }
      // }

      if (isOneAdd.value === false) {
        const arr = multipleSelection.value.map((item) => {
          return {
            guid: item.guid,
            zbrw: item.zbrw,
            mbyq: item.mbyq,
            ck: "delete",
          };
        });
        await saveDecomposition(arr);
      } else {
        for (const element of multipleSelection.value) {
          tableData.value = tableData.value.filter(
            (item) => item.guid !== element.guid,
          );
        }
      }

      if (isOneAdd.value === false) {
        await clickCurrentFJRW(clickObject.value, activeVar.value);
      }
    })
    .catch((error) => {
      // 处理错误
      console.error("Error during deletion:", error);
    });
};

const deletelhRW = (index: any, row: any) => {
  MBYQData.value.guid = row.guid;
  ElMessageBox.confirm("确定删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    if (isOneAdd.value == true) {
      zbgzTabledata.value.splice(index, 1);
    } else {
      const rrrr = [MBYQData.value];
      rrrr.forEach((ele) => {
        ele.ck = "delete";
      });
      await saveDecomposition([...rrrr]);
      await clickCurrentFJRW(clickObject.value, activeVar.value);
    }
  });
};

// 单条指标任务删除
const deleteRW = (index: any, row: any) => {
  // console.log(index,"---------",row)
  MBYQData.value.guid = row.guid;
  MBYQData.value.zbrw = row.zbrw;
  MBYQData.value.mbyq = row.mbyq;
  ElMessageBox.confirm("确定删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    if (isOneAdd.value == false) {
      const rrrr = [MBYQData.value];
      rrrr.forEach((ele) => {
        ele.ck = "delete";
      });
      await saveDecomposition([...rrrr]);
      await clickCurrentFJRW(clickObject.value, activeVar.value);
    } else {
      tableData.value.splice(index, 1);
    }
  });
};

const aaaa = ref();
const onSelectValue = async (row: any) => {
  if (row.guid === undefined) return;
  if (isOneAdd.value === false) {
    aaaa.value = row;
    aaaa.value.ck = "edit";
    aaaa.value.zbrw = clickObject.value.zbrw;
    aaaa.value.sflhzb = clickObject.value.sflhzb;
    const arr = [];
    for (let i = 0; i < lhzbArr.value.length; i++) {
      let element = aaaa.value;
      arr.push({
        yq: clickObject.value.mbyq,
        gz: element["selectValue" + i],
        mb: element["mb" + i],
        jg: lhzbArr.value[i],
        dw: "",
      });
    }
    aaaa.value.lhzbgz = JSON.stringify(arr);
    const { code } = await saveDecomposition([aaaa.value]);
    if (code === 0) {
      ElMessage.success("编辑成功");
    }
  }
};

// 确认分解
const confirmDecomposition = async () => {
  // console.log("clickObject.value.sflhzb:====",clickObject.value.sflhzb)
  if (clickObject.value.sflhzb == "是") {
    zbgzTabledata.value.map((item) => {
      item.explodeState = "当前子任务已分解！";
      item.ck = "edit";
      item.zbrw = clickObject.value.zbrw;
      item.sflhzb = clickObject.value.sflhzb;
      const arr = [];
      for (let i = 0; i < lhzbArr.value.length; i++) {
        let ele = item;
        arr.push({
          yq: clickObject.value.mbyq,
          gz: ele["selectValue" + i],
          mb: ele["mb" + i],
          jg: lhzbArr.value[i],
          dw: "",
        });
      }

      item.lhzbgz = JSON.stringify(arr);
    });
    await taskDecomposition([...zbgzTabledata.value]);
    await clickCurrentFJRW(clickObject.value, activeVar.value);
  } else {
    const { data } = await getAllSubtaskByTaskId(currentFJRW.value.item.guid);
    if (!data) {
      const arr = tableData.value.map((item) => {
        return { ...item, ck: "add" };
      });
      await saveDecomposition(arr);
      const { data: result } = await getAllSubtaskByTaskId(
        currentFJRW.value.item.guid,
      );
      const arr2 = result.map((item) => {
        return { ...item, ck: "edit" };
      });
      await taskDecomposition(arr2);
    } else {
      tableData.value.map((item) => {
        item.explodeState = "当前子任务已分解！";
        item.ck = "edit";
        item.sflhzb = clickObject.value.sflhzb;
      });
      await taskDecomposition([...tableData.value]);
    }
    await clickCurrentFJRW(clickObject.value, activeVar.value);
  }
  getCurrentYearAllByDepartmentName();
};

// 保存
const saveRWData = async () => {
  if (clickObject.value.sflhzb == "是") {
    if (zbgzMultipleSelection.value.length === 0 && isOneAdd.value === true) {
      zbgzMultipleSelection.value = zbgzTabledata.value;
    }
    // 有数据
    if (oldLhzbTableData.value.length !== 0) {
      zbgzMultipleSelection.value.forEach((element) => {
        element.ck = "add";
        oldLhzbTableData.value.filter((item: any) => {
          item.ck = "edit";
          const arr = [];
          for (let i = 0; i < lhzbArr.value.length; i++) {
            let element = item;
            arr.push({
              yq: clickObject.value.mbyq,
              gz: element["selectValue" + i],
              mb: element["mb" + i],
              jg: lhzbArr.value[i],
              dw: "",
            });
          }
          item.lhzbgz = JSON.stringify(arr);
        });
        const arr = [];
        for (let i = 0; i < lhzbArr.value.length; i++) {
          let item = element;
          arr.push({
            yq: clickObject.value.mbyq,
            gz: item["selectValue" + i],
            mb: item["mb" + i],
            jg: lhzbArr.value[i],
            dw: "",
          });
        }
        element.lhzbgz = JSON.stringify(arr);
      });
      await saveDecomposition([
        ...oldLhzbTableData.value,
        ...zbgzMultipleSelection.value,
      ]);
      await clickCurrentFJRW(clickObject.value, activeVar.value);
      zbgzMultipleSelection.value = [];
    } else {
      // 无数据;
      zbgzMultipleSelection.value.forEach((element) => {
        element.ck = "add";
        const arr = [];
        for (let i = 0; i < lhzbArr.value.length; i++) {
          let item = element;
          arr.push({
            yq: clickObject.value.mbyq,
            gz: item["selectValue" + i],
            mb: item["mb" + i],
            jg: lhzbArr.value[i],
            dw: "",
          });
        }
        element.lhzbgz = JSON.stringify(arr);
      });
      await saveDecomposition([...zbgzMultipleSelection.value]);
      await clickCurrentFJRW(clickObject.value, activeVar.value);
      zbgzMultipleSelection.value = [];
    }
  } else {
    // 没选择全部保存
    if (multipleSelection.value.length === 0) {
      multipleSelection.value = tableData.value;
    }
    if (oldTableData.value !== null) {
      multipleSelection.value.forEach((element) => {
        element.ck = "add";
        if (element.guid === undefined) {
          oldTableData.value.filter((item: any) => {
            item.ck = "edit";
          });
        }
      });
      await saveDecomposition([
        ...oldTableData.value,
        ...multipleSelection.value,
      ]);
      await clickCurrentFJRW(clickObject.value, activeVar.value);
      multipleSelection.value = [];
    } else {
      const { data } = await getAllSubtaskByTaskId(currentFJRW.value.item.guid);
      if (!data) {
        multipleSelection.value.forEach((element) => {
          element.ck = "add";
        });
      } else {
        multipleSelection.value.forEach((element) => {
          element.ck = "edit";
        });
      }
      await saveDecomposition([...multipleSelection.value]);
      await clickCurrentFJRW(clickObject.value, activeVar.value);
      multipleSelection.value = [];
    }
  }
};

// 取消
const cancel = () => {
  addDialogVisible.value = false;
  form.value = {
    mbyq: "",
    ssbm: "",
  };
};

const cancelLhzb = () => {
  addLhzbDialogVisible.value = false;
  addForm.value = {
    ssbm: "",
  };
};

// 量化指标弹框确定
const addLhzbSubmit = () => {
  isOneAdd.value = false;
  if (zbgzTabledata.value.length !== 0) {
    let temp = {
      ssbm: addForm.value.ssbm,
      sjrw: clickObject.value.guid,
      zbrw: clickObject.value.zbrw,
      mbyq: clickObject.value.mbyq,
      sflhzb: clickObject.value.sflhzb,
      explodeState: "当前子任务未分解！",
    };
    for (let i = 0; i < lhzbArr.value.length; i++) {
      let name = "selectValue" + i;
      temp[name] = "大于等于";
    }
    zbgzTabledata.value.push(temp);
    zbgzMultipleSelection.value.push(temp);
    addLhzbDialogVisible.value = false;
    addForm.value.ssbm = "";
  } else {
    let temp = [
      {
        ssbm: addForm.value.ssbm,
        sjrw: clickObject.value.guid,
        explodeState: "当前子任务未分解！",
        zbrw: clickObject.value.zbrw,
        mbyq: clickObject.value.mbyq,
        sflhzb: clickObject.value.sflhzb,
      },
    ];
    zbgzTabledata.value = temp;
    zbgzTabledata.value.map((ele) => {
      for (let i = 0; i < lhzbArr.value.length; i++) {
        let name = "selectValue" + i;
        ele[name] = "大于等于";
      }
      return ele;
    });
    zbgzMultipleSelection.value.push(...temp);
    addLhzbDialogVisible.value = false;
    addForm.value.ssbm = "";
  }
};

const handleSearch = async (val: string) => {
  const { data } = await GetFJZBCX(val);
  ZBRWData.value = data;
  getExplodeState();
};

const handleFilter = async (type: string) => {
  if (type === "all") {
    getCurrentYearAllByDepartmentName();
  }
  if (type === "department") {
    await getCurrentYearAllByDepartmentName();
    ZBRWData.value = ZBRWData.value.filter((item) => item.sfbmtxxyjck === "是");
  }
  if (type === "college") {
    await getCurrentYearAllByDepartmentName();
    ZBRWData.value = ZBRWData.value.filter((item) => item.sfbmtxxyjck === "否");
  }
};

onMounted(() => {
  getCurrentYearAllByDepartmentName();
  getCollegeInfo();
});
</script>

<style lang="less" scoped>
.search {
  padding: 5px;
}

h2 {
  text-align: center;
  color: #000;
}
.el-dialog {
  .el-col-4 {
    border: 1px solid rgb(236, 238, 244);
    height: calc(100vh - 230px);
  }
}

:deep(.el-dialog__body) {
  padding: 0px 10px 10px 10px;
  margin: 0;
}

.right {
  margin: 10px;
  margin: 10px;

  .right-top {
    margin-bottom: 10px;
  }

  span {
    color: red;
    margin-left: 20px;
  }
  .right-one {
    .tabledata {
      border: 1px solid rgb(236, 238, 244);
      margin-top: 10px;
    }
  }
  .right-two {
    padding: 10px 20px 0px 20px;

    :deep(h2) {
      margin-top: 0px;
    }
    p {
      font-size: 15px;
      margin: 4px;
    }
    .zbgzTabledata {
      margin-top: 10px;
      border: 1px solid rgb(236, 238, 244);
      .parent {
        display: flex;
        .son2 {
          margin-left: 10px;
        }
      }
      .el-table {
        height: calc(100vh - 410px);
        .el-select {
          :deep(.el-input__inner) {
            width: 70px;
          }
        }
      }
    }
    .bottom-btn {
      margin-top: 10px;
      margin-left: 350px;
    }
  }
}
li {
  padding: 2px;
  width: 193px;
  overflow: hidden; //块元素超出隐藏
  text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
  white-space: nowrap; //规定段落中的文本不进行换行
}
li:hover {
  background-color: #ccc;
}
.el-scrollbar {
  height: calc(100vh - 310px);
}

.el-table {
  height: calc(100vh - 285px);
}
.activeCss {
  background-color: #ccc;
}
</style>

interface GET_ALLND_Type {
  nd: string;
  name: string;
  children: any[] | null;
}
interface GET_RWBYND_Type {
  nd: string;
  bmid: string;
  zblx: string;
  name: string;
  datatype: string;
  px: number;
  ssbm: string;
}

interface GET_TEACHERINFO_Type {
  userid: string;
  username: string;
}

interface GET_COLLEGEINFO_Type {
  departmentid: string;
  departmentname: string;
  datatype: string;
  px: number;
}

interface GET_ALLZNBMRW_Type {
  nd: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  ssbm: string;
  sflhzb: string;
  nzpj: string;
  xyzp: string;
  ssmk: string;
  zbly: Array;
  spzt: string;
  guid: string;
  hosttaskid: string;
  count: number;
  page: number;
  limit: number;
  dddd: boolean;
  searchParams: {
    dpzt?: string;
    zbrw: string;
    mbyq: string;
    zblx: string;
    ssbm?: string;
    fzrxm?: string;
    usename?: string;
  };
}

interface GET_XYKP_Type {
  nd: string;
  bmgxypj: string;
  xyzp: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  ssmk: string;
  ssbm: string;
  fzrxm: string;
  xdbm: string;
  bmrwfzrxm: string;
  count: number;
  page: number;
  limit: number;
}
interface Result<T = any> {
  code: number;
  count: number;
  data: T;
  msg: string;
}

interface GET_ADDUPDATERW_Type {
  hosttaskid?: string;
  guid?: string;
  zbrw?: string;
  mbyq?: string;
  zblx?: string;
  bz?: string;
  sflhzb?: string;
  bmssmbzrlx?: string;
  sjrw?: string;
  wcqk?: string;
  ck?: string;
  zrlx?: string;
  ssbm?: Array;
  zbly?: Array;
  nd?: string;
  dqzt?: string;
  hxxzb?: string;
  createtime?: string;
  ssmk?: string;
  sfbmtxxyjck?: string;
  xbbm?: Array;
  lhzbgz?: string;
  yjzb?: string;
  ejzb?: string;
  sjzb?: string;
}
interface GET_RWQK_Type {
  guid: string;
  mbrwguid: string;
  wcqk: string | null;
  sqsj: string;
  fj: Array | null;
  zpjg: string | null;
  xzcs: number;
  fjshow?: boolean;
}

interface GET_BCSM_Type {
  guid: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  bz: string | null;
  sjrw: string | null;
  explodeState: string;
  wcqk: string;
  ck: string | null;
  zrlx: string | null;
  ssbm: string;
  zbly: string;
  nd: string;
  znbmtjr: string | null;
  Fzghcshr: null;
  dqzt: string;
  fK_GUID: string | null;
  hxxzb: string;
  createtime: string;
  xbbm: string | null;
  xyzp: string;
  xyzpfbzt: string;
  nzpj: string;
  nzpjfbzt: string;
  hosttaskid: string | null;
  zpsbincident: string;
  fzrgh: string;
  fzrxm: string;
  bmgxypj: string | null;
  bmgxypjfbzt: string | null;
  bcsm: string | null;
  oldguid: string | null;
  oldtaskid: string;
  rwpx: number;
  lhzbgz: string;
  xh: number;
  spzt: string;
  sflhzb: string;
  wcsj: string;
  sjxh: string | null;
  ssmk: string | null;
  bmssmbzrlx: string;
  sfbmtxxyjck: string;
  spztlj: string;
}
interface GET_RWFJZT_Type {
  guid: string;
  nd: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  spzt: string;
  bz: string | null;
  sjrw: string;
  ck: string | null;
  zrlx: string;
  xtpj: string | null;
  xyzp: string;
  xyzpfbzt: string;
  nzpj: string;
  nzpjfbzt: string;
  bmgxypj: string;
  bmgxypjfbzt: string;
  bcsm: string | null;
  ssbmpx: number;
  bmid: string;
  ssbm: string;
  zbly: string;
  znbmtjr: string | null;
  fzghcshr: string | null;
  dqzt: string;
  fK_GUID: string | null;
  hxxzb: string;
  createtime: string;
  hosttaskid: string | null;
  fzrgh: string;
  fzrxm: string;
  sfwc: string | null;
  datatype: string;
  wcqk: string;
  wcsj: string;
  fj: Array | null;
  taskid: string;
  xdbm: string;
  sflhzb: string;
  oldguid: string | null;
  oldtaskid: string;
  rwpx: number;
  xbbm: string | null;
  bmrwfzrxm: string;
  ssmk: string;
  sfbmtxxyjck: string;
  zpsbincident: string;
  bmssmbzrlx: string | null;
  row_num: string | null;
  explodeState: string;
  lhzbgz: string;
  spztlj: string;
  dddd: boolean;
  sjzb: string;
  searchParams: {
    dpzt?: string;
    zbrw?: string;
    mbyq?: string;
    zblx?: string;
    ssbm?: string;
    fzrxm?: string;
    xyzpfbzt?: string;
    type?: string;
  };
}

interface POST_ADDLHZB_Type {
  guid: string;
  zbrw: string;
  mbyq: string;
  zblx: string;
  bz: string;
  sjrw: string;
  wcqk: string;
  ck: string;
  zrlx: string;
  ssbm: string;
  zbly: string;
  nd: string;
  znbmtjr: string;
  fzghcshr: string;
  dqzt: string;
  fK_GUID: string;
  hxxzb: string;
  spztlj: string;
  spzt: string;
  createtime: string;
  xbbm: string;
  xyzp: string;
  xyzpfbzt: string;
  nzpj: string;
  nzpjfbzt: string;
  hosttaskid: string;
  zpsbincident: string;
  fzrgh: string;
  fzrxm: string;
  bmgxypj: string;
  bmgxypjfbzt: string;
  bcsm: string;
  oldguid: string;
  oldtaskid: string;
  rwpx: number;
  xh: number;
  sjxh: number;
  ssmk: string;
  bmssmbzrlx: string;
  sfbmtxxyjck: string;
  explodeState: string;
  sflhzb: string;
  lhzbgz: string;
}

interface GET_HQWCQK_type {
  guid: string;
  rwid: string;
  bmid: string;
  zb: string;
  zrbm: string;
  wcdjsjy: string;
  wcqksjy: string;
}

interface Get_ZBJB_type {
  guid: string;
  mc: string;
  sjmc: string;
  sjid: string;
  xjzb: Get_ZBJB_type[];
  children?: Get_ZBJB_type[]; // 子节点数组（可选）
  bmmc: string;
  bmbm: string;
  sfqy: string; // 是否启用字段
}

interface GET_permission_Type {
  code: number;
  count: number;
  data: T;
  msg: string;
}

interface GET_COLLEGEINFO_Type {
  departmentid: string;
  departmentname: string;
  datatype: string;
  px: number;
}

interface GET_WFJBM_TYPE {
  bmbm: string;
  bmmc: string;
  guid: string;
}

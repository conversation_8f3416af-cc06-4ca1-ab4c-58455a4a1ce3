<!--任务报评-->
<template>
  <el-row>
    <el-col :span="20">
      <div class="left">
        <div class="header">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <!--            <el-form-item label="一级指标">-->
            <!--              <el-select-->
            <!--                v-model="formInline.yjzb"-->
            <!--                placeholder="请选择"-->
            <!--                clearable-->
            <!--                @change="(val:string) => changeYjzb(val)"-->
            <!--              >-->
            <!--                <el-option-->
            <!--                  v-for="item in currentYjzb"-->
            <!--                  :key="item.guid"-->
            <!--                  :label="item.yjzb"-->
            <!--                  :value="item.yjzb"-->
            <!--                />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <!--            <el-form-item label="二级指标">-->
            <!--              <el-select-->
            <!--                v-model="formInline.ejzb"-->
            <!--                placeholder="请选择"-->
            <!--                clearable-->
            <!--              >-->
            <!--                <el-option-->
            <!--                  v-for="item in currentEJZB"-->
            <!--                  :key="item.guid"-->
            <!--                  :value="item.ejzb"-->
            <!--                  :label="item.ejzb"-->
            <!--                />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="年度">
              <el-select v-model="year" @change="handleChange">
                <el-option label="2025" value="2025"></el-option>
                <el-option label="2024" value="2024"></el-option>
                <el-option label="2023" value="2023"></el-option>
                <el-option label="2022" value="2022"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="指标等级">
              <el-input
                v-model="formInline.sjzb"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="考核内容及标准">
              <el-input
                v-model="formInline.zbrw"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="负责人：">
              <el-input
                v-model="formInline.fzrxm"
                placeholder="请输入"
                @keyup.enter="searchData"
              />
            </el-form-item>
            <el-form-item label="下达部门">
              <el-select
                v-model="formInline.xdbm"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in xdbmData"
                  :key="item.departmentid"
                  :label="item.departmentname"
                  :value="item.departmentname"
                />
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="自评状态">-->
            <!--              <el-select-->
            <!--                v-model="formInline.xyzpfbzt"-->
            <!--                placeholder="请选择"-->
            <!--                clearable-->
            <!--              >-->
            <!--                <el-option label="未提交" value="待发布" />-->
            <!--                <el-option label="审批中" value="正在审批" />-->
            <!--                <el-option label="已审核" value="已发布" />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->

            <el-button type="primary" @click="searchData">搜索</el-button>
            <el-button @click="onAllBtnClick" style="margin-right: 20px">
              全部
            </el-button>
          </el-form>
        </div>
        <div class="center-btn">
          <div class="btn-left"></div>
          <div class="btn-right">
            <el-button type="primary" @click="handleClick">
              突破性指标申报
            </el-button>
            <el-button type="primary" @click="submitEvaluate">
              提交评价
            </el-button>
            <el-tooltip
              content="默认导出本人指标，若需导出所有指标，请先点击“全部”按钮"
              effect="light"
            >
              <el-button @click="exportExcel">导出</el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="tabledata">
          <el-table
            ref="multipleTableRef"
            :data="tableData"
            style="width: 100%"
            :cell-style="columnStyle"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            highlight-current-row
            v-loading="loading"
            :row-key="
              (row: any) => {
                return row.guid;
              }
            "
          >
            <el-table-column
              type="selection"
              width="30"
              :selectable="selectable"
              fixed
              :reserve-selection="true"
            />
            <el-table-column
              property="spzt"
              label="审核情况"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <div
                  @click="onClickSpzt(row)"
                  style="cursor: pointer"
                  v-if="
                    row.spzt == '已完成' ||
                    row.spzt == '审批中' ||
                    row.spzt == '正在审批'
                  "
                >
                  {{ row.spzt }}
                </div>
              </template>
            </el-table-column>
            <el-table-column property="bmgxypj" label="考核部门评分" />
            <el-table-column
              property="xx"
              label="学院完成情况"
              width="140"
              sortable
              align="center"
              show-overflow-tooltip
            />
            <el-table-column label="自评分数" width="120" align="center">
              <template #default="{ row }">
                <el-input
                  v-model="row.xyzp"
                  placeholder="请输入数字"
                  @change="(val: number) => onChangeInput(val, row)"
                  v-if="handleDisabled(row)"
                  :disabled="row.sfyc == '是'"
                />

                <el-button
                  v-else-if="handleShowBtn2(row)"
                  @click.stop="handleLook(row)"
                >
                  评分
                </el-button>
                <el-button
                  v-else-if="handleShowBtn(row)"
                  @click.stop="handleLook(row)"
                >
                  查看
                </el-button>
                <el-tag
                  v-else-if="row.sfbmtxxyjck === '是'"
                  class="tag1"
                  style="border: none"
                  size="small"
                >
                  下达部门填
                </el-tag>
                <span v-else-if="row.sfbmtxxyjck === '否'">{{ row.xyzp }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              property="name"
              label="学院完成情况"
              width="120"
              align="center"
            >
              <template #default="{ row }">
                <el-button
                  v-if="row.sfbmtxxyjck == '否' && row.zrlx == '系统集成'"
                  size="small"
                  type="danger"
                  @click="ckXYWCQK(row)"
                >
                  查看
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  v-if="row.sfbmtxxyjck == '是' && row.zrlx == '自主填报'"
                  @click="tbXYWCQK(row)"
                >
                  部门填
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  v-if="row.sfbmtxxyjck == '是'"
                  @click="tbXYWCQK(row)"
                >
                  部门填
                </el-button>
              </template>
            </el-table-column> -->
            <el-table-column
              property="fzrxm"
              label="负责人"
              sortable
              width="150"
              align="center"
              show-overflow-tooltip
            />
            <!-- <el-table-column
              property="yjzb"
              label="一级指标"
              sortable
              show-overflow-tooltip
              min-width="120px"
            >
            </el-table-column>
            <el-table-column
              property="ejzb"
              label="二级指标"
              sortable
              show-overflow-tooltip
              min-width="120px"
            >
            </el-table-column> -->
            <el-table-column
              property="sjzb"
              label="指标等级"
              sortable
              show-overflow-tooltip
              min-width="150px"
            ></el-table-column>

            <el-table-column
              property="zbrw"
              label="考核内容及标准"
              sortable
              min-width="150px"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <el-tag
                  class="tag1"
                  v-if="row.sfbmtxxyjck == '是' && row.zrlx == '自主填报'"
                  style="border: none"
                  size="small"
                >
                  下达部门填
                </el-tag>
                <el-tag
                  class="tag2"
                  v-if="
                    row.wcqk !== null &&
                    row.wcqk !== '' &&
                    row.sfbmtxxyjck == '否' &&
                    row.zrlx == '自主填报'
                  "
                  style="border: none"
                  size="small"
                >
                  已填报
                </el-tag>
                <el-tag
                  class="tag3"
                  v-if="row.zrlx == '系统集成'"
                  style="border: none"
                  size="small"
                >
                  系统抽
                </el-tag>
                <el-tag
                  class="tag3"
                  v-if="row.hosttaskid !== null"
                  style="border: none"
                  size="small"
                >
                  协办
                </el-tag>
                <span>{{ row.zbrw }}</span>
              </template>
              <!-- <template #default="{ row }">
                <el-popover
                  v-if="delHtmlZbrw(row.zbrw).length > 0"
                  popper-class="dddd"
                  effect="light"
                  trigger="hover"
                  placement="bottom"
                  width="auto"
                >
                  <template #default>
                    <p v-for="item of delHtmlZbrw(row.zbrw)">{{ item }}</p>
                  </template>
                  <template #reference>
                    <p class="one-show">
                      {{ row.zbrw }}
                    </p>
                  </template>
                </el-popover>
                <el-tooltip
                  v-else
                  popper-class="dddd"
                  effect="light"
                  :content="row.zbrw"
                  placement="bottom"
                >
                  <p class="one-show">
                    {{ row.zbrw }}
                  </p>
                </el-tooltip>
              </template> -->
            </el-table-column>
            <el-table-column
              property="mbyq"
              label="评分办法"
              sortable
              align="center"
              show-overflow-tooltip
            />
            <el-table-column
              property="xdbm"
              label="下达部门"
              sortable
              align="center"
              width="140"
              show-overflow-tooltip
            />

            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <!-- <el-button
                  type="danger"
                  @click="reportingOnclick(row)"
                  v-if="
                    row.yjzb == '组织建设' ||
                    row.yjzb == '统战工作' ||
                    row.yjzb == '作风建设' ||
                    row.yjzb == '宣传工作'
                  "
                >
                  报评
                </el-button> -->
                <el-button
                  type="danger"
                  @click="bgReportingOnclick(row)"
                  :disabled="row.sfyc == '是'"
                  v-if="
                    row.fzrgh
                      ? row.fzrgh.split(',').includes(user?.profile.UserId) &&
                        row.sfbmtxxyjck !== '是'
                      : false
                  "
                >
                  报评
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="right-page">
          <el-pagination
            v-model:current-page="page"
            v-model:page-size="limit"
            :page-sizes="[10, 15, 20, 25, 50, 100, 1000]"
            layout=" prev, pager, next, jumper,total, sizes"
            :total="total"
            class="page-fen"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-col>
    <el-col :span="4">
      <div class="left-header">
        <h2>目标完成情况</h2>
        <span>{{ currentClickRw }}</span>
      </div>
      <el-divider style="margin: 10px 0" />
      <el-scrollbar>
        <div v-if="eeee == '系统集成'">
          <h3 style="text-align: center; margin-bottom: 5px">
            本部门已完成：{{ currentCount }}项
          </h3>
          <span
            style="
              display: block;
              text-align: center;
              font-size: 12px;
              color: rgb(148, 148, 148);
            "
          >
            同步时间：{{ nowdataData }} 02:00:00
          </span>
          <div style="text-align: center">
            <router-link
              target="_blank"
              :to="{
                path: '/WCQK',
                query: { guid: rrrr.guid, depName: curdep },
              }"
              style="padding: 10px; color: #2a59b6; cursor: pointer"
            >
              查看完成情况
            </router-link>
          </div>
          <div class="bottom-plugin" style="padding: 10px; margin-top: 400px">
            <el-divider style="margin: 10px 0" />
            <h2>
              补充说明情况
              <el-button
                type="primary"
                style="margin-left: 5px"
                @click="onEnterInfo"
                v-if="bclrsmBtnShow"
              >
                录入
              </el-button>
            </h2>
            <el-divider style="margin: 10px 0" />
            <span>{{ delHtmlTag(currentBCSM) }}</span>
          </div>
        </div>
        <div class="aaaa" v-else>
          <div class="header-btn">
            <!-- <el-button
              type="primary"
              size="small"
              class="header-btn"
              @click="sendCompete"
              id="sendCompeteid"
              v-if="sbwcqkBtnShow"
            >
              上报完成情况
            </el-button> -->
          </div>
          <div class="connent" v-if="isShow" v-for="item in checkPluginData">
            <span>修订时间：{{ item.sqsj }}</span>
            <br />
            <span>完成情况：{{ item.wcqk }}</span>
            <p>{{ currentWCQK }}</p>
            <div class="fujian" v-if="item.fj">
              <h3>附件：</h3>
              <div class="fujian-plugin">
                <!--                <a-->
                <!--                  @click="onFy(item.url)"-->
                <!--                  style="cursor: pointer"-->
                <!--                  v-for="(item, index) of item.fj"-->
                <!--                  :key="index"-->
                <!--                >-->
                <!--                  {{ item.name }}-->
                <!--                </a>-->

                <div v-for="(fjItem, index) in item.fj" :key="index">
                  {{ fjItem.name }}
                  <el-button size="small" @click="onFy(fjItem.url)">
                    预览
                  </el-button>
                  <el-button
                    size="small"
                    @click="downloadAttachment(fjItem.url, fjItem.name)"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
            <!-- <div class="connent" v-if="isShow">
            <span>修订时间：{{ currentWCS }}</span>
            <br />
            <span>完成情况：{{ currentWCQK }}</span>
            <p>{{ currentWCQK }}</p>
            <div class="fujian" v-if="fjshow">
              <h3>附件：</h3>
              <div class="fujian-plugin">
                <a
                  @click="onFy(item.url)"
                  style="cursor: pointer"
                  v-for="(item, index) of currentFJ"
                  :key="index"
                  >{{ item.name }}</a
                >
              </div>
            </div> -->
            <div class="header-btn">
              <!-- <el-button type="primary" size="small" @click="checkPlugin">
                查看详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="editCompete"
                v-if="reapplyBtnShow"
                >修改</el-button
              > -->
            </div>
          </div>
          <!-- <div class="header-btn">
            <el-button
              type="warning"
              size="small"
              @click="checkLastYear"
              style="margin-top: 5px"
              v-if="qnShow"
            >
              查看去年填报情况
            </el-button>
          </div> -->
        </div>
      </el-scrollbar>
    </el-col>
  </el-row>
  <!-- <el-dialog
    :title="bgdialogTitle"
    v-model="bgDialogVisible"
    width="1200"
    hight="700px"
    destroy-on-close
    class="completeDialog"
  >
    <DialogPlugin
      :bgdialogTitle="bgdialogTitle"
      @onDialogVisibleSubmit="dialogSubmit"
    ></DialogPlugin>
  </el-dialog> -->
  <el-dialog
    v-model="dialogFormVisible"
    title="部门目标（各学院完成情况）"
    width="1450"
    hight="300px"
    destroy-on-close
  >
    <ckDialogPlugin :currentClick="currentClick" />
  </el-dialog>
  <el-dialog
    v-model="exportVisible"
    title="选择导出字段"
    width="500px"
    hight="300px"
    destroy-on-close
  >
    <el-checkbox-group v-model="checkList">
      <el-checkbox
        v-for="(item, index) in exportZDList"
        :key="index"
        :label="item.value"
        size="large"
      >
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="exportSumbit">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dialogEnterInfo"
    title="录入补充说明"
    width="600"
    hight="300"
    destroy-on-close
  >
    <el-input
      v-model="enterinfo"
      type="textarea"
      placeholder="请输入"
      :autosize="{ minRows: 5, maxRows: 20 }"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="editSubmit()">确定</el-button>
        <el-button @click="dialogEnterInfo = false">取消</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog v-model="collegeCompletionDetailVisible" width="80%" top="60px">
    <el-table :data="collegeCompletionDetail">
      <el-table-column property="sjzb" label="指标等级" width="200px" />
      <el-table-column property="zbrw" label="考核内容及标准" width="200px" />
      <el-table-column property="mbyq" label="评分办法" />
      <el-table-column property="ssbm" label="所属部门" width="200px" />
      <el-table-column property="bmgxypj" label="考核评分" width="200px">
        <template #default="{ row }">
          <el-input
            v-if="dddd"
            v-model="row.bmgxypj"
            @input="handleSave(row)"
          />
          <span v-else>{{ row.bmgxypj }}</span>
        </template>
      </el-table-column>
      <el-table-column property="wcqk" label="完成情况" width="200px">
        <template #default="{ row }">
          <el-input v-if="dddd" v-model="row.wcqk" @input="handleSave(row)" />
          <span v-else>{{ row.wcqk }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  getCurrentYearRWByDepartmentName,
  getAllDep,
  getWCQKByRWId,
  getbrfzrwzbs,
  getzbshqk,
  getCurrentUserDepID,
  getCurrentUserMenuPermission,
  getNowDate,
  getCurrentUserDepName,
  getRwmrfzr,
  getBBMEJZB,
  getBBMSSMK,
  getBCSMById,
  getHQWCQK,
  saveRWBCSM,
  bmtxSaveEvaluate,
  getBMYJZB,
  saveWCQK,
} from "@/api/TYJK/index";
import ckDialogPlugin from "./components/ckDialogPlugin.vue";
import { onBeforeMount, onMounted } from "vue";
import { ref, reactive } from "vue";
import { useAuth } from "vue-oidc-provider";
import { ElMessage, ElMessageBox } from "element-plus";
import axios from "axios";
import { useRoute } from "vue-router";
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
import router from "@/router";
import { getCollegeCompletionDetailByTaskId } from "@/api/BMKH";
const route = useRoute();

const currentClick = ref();
const bgdialogTitle = ref();
const { user } = useAuth();
const checkList = ref([
  "spzt",
  "sjzbfz",
  "fzrxm",
  "yjzb",
  "ejzb",
  "sjzb",
  "mbyq",
  "zbrw",
  "zblx",
  "ssbm",
  "xbbm",
  "wcqk",
  "fj",
]);
const dialogFormVisible = ref(false);
const fjshow = ref(false);
const pppp = ref();
const dialogEnterInfo = ref<boolean>(false);
const multipleTableRef = ref();
const bgDialogVisible = ref<boolean>(false);
const declarationShow = ref<boolean>(false);
const exportZDList = [
  {
    label: "评分",
    value: "spzt",
  },
  // {
  //   label: "分值",
  //   value: "sjzbfz",
  // },
  // {
  //   label: "一级指标",
  //   value: "yjzb",
  // },
  // {
  //   label: "二级指标",
  //   value: "ejzb",
  // },
  {
    label: "指标等级",
    value: "sjzb",
  },
  {
    label: "目标任务",
    value: "zbrw",
  },
  {
    label: "评分方法",
    value: "mbyq",
  },
  {
    label: "负责人",
    value: "fzrxm",
  },
  {
    label: "责任部门",
    value: "ssbm",
  },
  {
    label: "协办部门",
    value: "xbbm",
  },
  {
    label: "完成情况",
    value: "wcqk",
  },
  {
    label: "有无附件",
    value: "fj",
  },
];
const exportVisible = ref<boolean>(false);
// 返回值改变颜色
const columnStyle = ({ columnIndex, row }: any) => {
  if (
    (columnIndex == 1 && row.spzt == "已完成") ||
    (columnIndex == 1 && row.spzt == "审批中") ||
    (columnIndex == 1 && row.spzt == "正在审批")
  ) {
    return {
      color: "blue",
    };
  }
};

const formInline = reactive<any>({
  yjzb: "",
  ejzb: "",
  sjzb: "",
  zbrw: "",
  xyzpfbzt: "",
  fzrxm: "",
  xdbm: "",
});
// 处理返回值标签
const delHtmlTag = (str: any) => {
  if (str !== null) {
    return str.replaceAll(/<br\/>/gi, "");
  }
};
const delHtmlZbrw = (str: any) => {
  if (!str) {
    return false;
  }
  return Array.from(
    str.matchAll(/(\d+)\.(.*?)(?=\d+\.|$)/g),
    (match: any[]) => match[0],
  );
};
const completeDialogVisible = ref<boolean>(false);
const isShow = ref<boolean>(false);
const currentWCS = ref<string>("");
const currentWCQK = ref<string | null>("");
const currentRWCount = ref<number>(0);
const currentFJ = ref({
  item: {
    name: "",
    url: "",
  },
});
const eeee = ref();
const currentBCSM = ref<string | null>("");
const currentClickXyzp = ref<string>("");
const checkPluginData = ref<any[]>([]);
const qnShow = ref(false);
const tbdialogFormVisible = ref<boolean>(false);
const sbwcqkBtnShow = ref<boolean>(false); //上报完成情况
const bclrsmBtnShow = ref<boolean>(false); //补充录入说明
const reapplyBtnShow = ref<boolean>(false); //修改
const iframeUrl = ref<string>("");
const rrrr = ref();
const currentClickRw = ref<string>("");
const type = ref<string>("");
const usercode = ref<number>(0);
const loading = ref<boolean>(false);
const page = ref<number>(1);
const limit = ref<number>(15);
const total = ref<number>(0);
const multipleSelection = ref<GET_RWFJZT_Type[]>([]);
const tableData = ref<GET_RWFJZT_Type[]>([]);
const zbshqk = ref<string>("");
const brfzrwzb = ref<string>("");
const onqbClick = ref<boolean>(false);
const tbcurrentClick = ref();
const collegeCompletionDetail =
  ref<Array<GetCollegeCompletionDetailByTaskIdResult>>();
const collegeCompletionDetailVisible = ref<boolean>(false);
const year = ref<string>("2024");

const ckXYWCQK = (row: any) => {
  currentClick.value = row;
  dialogFormVisible.value = true;
};

const tbXYWCQK = (row: any) => {
  tbcurrentClick.value = row;
  tbdialogFormVisible.value = true;
};

const handleSelectionChange = (val: GET_RWFJZT_Type[]) => {
  multipleSelection.value = val;
};

const handleCurrentChange = (pageIndex: number) => {
  page.value = pageIndex;
  if (onqbClick.value == true) {
    onAllBtnClick();
  }
  if (onqbClick.value === false) {
    if (searchClick.value == true) {
      cxbgData();
    }
    if (searchClick.value == false) {
      if (formInline.fzrxm !== user.value?.profile.UserName) {
        cxbgData();
      } else {
        getCurrentYearDepartmentName();
      }
    }
  }
};

const handleSizeChange = (num: number) => {
  limit.value = num;
  if (onqbClick.value == true) {
    onAllBtnClick();
  }
  if (onqbClick.value === false) {
    if (searchClick.value == true) {
      cxbgData();
    }
    if (searchClick.value == false) {
      if (formInline.fzrxm !== user.value?.profile.UserName) {
        cxbgData();
      } else {
        getCurrentYearDepartmentName();
      }
    }
  }
};

// 多选某些情况下禁用
const selectable = (row: any) => {
  if (
    row.hosttaskid !== null ||
    // row.fzrxm !== user.value?.profile.UserName ||
    // row.fzrxm?row.fzrxm : !row.fzrxm.split(',').includes(user.value?.profile.UserName) ||
    row.fzrgh
      ? !row.fzrgh.split(",").includes(user.value?.profile.UserId)
      : row.fzrgh ||
        row.xyzpfbzt == "正在审批" ||
        // row.xyzpfbzt == "已发布" ||
        row.zblx == "部门自拟指标" ||
        row.xyzp == "" ||
        row.xyzp == null ||
        row.sfyc == "是"
  ) {
    return false;
  } else {
    return true;
  }
};

const cxbgData = async () => {
  try {
    loading.value = true;
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      zbrw: formInline.zbrw,
      xyzpfbzt: formInline.xyzpfbzt,
      fzrxm: formInline.fzrxm,
      xdbm: formInline.xdbm,
      ssbm: route.query.ssbm ? route.query.ssbm : "",
    };
    const { data, count } = await getCurrentYearRWByDepartmentName(
      page.value,
      limit.value,
      JSON.stringify(requestInfo),
      "",
      route.query.code as string,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const searchClick = ref(false);
// 搜索时触发
const searchData = async () => {
  onqbClick.value = false;
  searchClick.value = true;
  try {
    loading.value = true;
    const requestInfo = {
      yjzb: formInline.yjzb,
      ejzb: formInline.ejzb,
      sjzb: formInline.sjzb,
      zbrw: formInline.zbrw,
      xyzpfbzt: formInline.xyzpfbzt,
      fzrxm: formInline.fzrxm,
      xdbm: formInline.xdbm,
      ssbm: route.query.ssbm ? route.query.ssbm : "",
    };
    const { data, count } = await getCurrentYearRWByDepartmentName(
      (page.value = 1),
      (limit.value = 15),
      JSON.stringify(requestInfo),
      "",
      route.query.code as string,
    );

    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

const currentParams = ref();
const currentCount = ref();
// 单击每一行任务
const handleRowClick = async (row: any) => {
  if (row.oldtaskid !== null && row.oldtaskid !== "") {
    qnShow.value = true;
  } else {
    qnShow.value = false;
  }
  currentClickRw.value = row.zbrw;
  rrrr.value = row;
  eeee.value = row.zrlx;
  // currentClickXyzp.value = row.xyzp;
  if (row.zrlx === "系统集成") {
    const res1 = await getHQWCQK(row.guid, row.ssbm);
    if (res1.code == 200) {
      currentParams.value = res1.data;
      const res2 = await axios.get(
        `https://ydmh.guangshaxy.com/formWebSite/form/api/DataSource/GetDataSourcePageByNo?sqlNo=${currentParams.value}`,
        {
          headers: {
            Authorization: `Bearer ${user.value?.access_token}`,
          },
        },
      );
      if (res2.data.count !== 0) {
        currentCount.value = res2.data.count;
      } else {
        currentCount.value = 0;
      }
    } else {
      currentCount.value = 0;
    }
    const { data } = await getBCSMById(row.guid);
    currentBCSM.value = data[0].bcsm;
    if (user.value?.profile.UserId === row.fzrgh) {
      bclrsmBtnShow.value = true;
    } else {
      bclrsmBtnShow.value = false;
    }
  } else {
    const { data, count } = await getWCQKByRWId(row.guid);
    console.log(8888);

    if (data.length == 0) {
      isShow.value = false;
    } else {
      isShow.value = true;
    }
    // 自主填报
    if (row.zrlx == "自主填报" && row.hosttaskid == null) {
      sbwcqkBtnShow.value = true;
      if (row.wcqk == null || row.wcqk == "") {
        reapplyBtnShow.value = false;
        if (row.fzrgh == user.value?.profile.UserId) {
          sbwcqkBtnShow.value = true;
        } else {
          sbwcqkBtnShow.value = false;
        }
      } else {
        sbwcqkBtnShow.value = false;
        if (row.xyzpfbzt != "正在审批" && row.xyzpfbzt != "已发布") {
          if (row.fzrgh == user.value?.profile.UserId) {
            reapplyBtnShow.value = true;
          } else {
            reapplyBtnShow.value = false;
          }
        } else {
          reapplyBtnShow.value = false;
        }
      }
      if (row.hosttaskid == null) {
        if (row.fzrgh == user.value?.profile.UserId) {
          if (row.xyzpfbzt == "待发布" || row.xyzpfbzt == null) {
            reapplyBtnShow.value = true;
          } else {
            reapplyBtnShow.value = false;
          }
        }
      } else {
        reapplyBtnShow.value = false;
      }
      if (row.zblx == "部门自拟指标") {
        sbwcqkBtnShow.value = false;
      }
    } else {
      sbwcqkBtnShow.value = false;
    }
    checkPluginData.value = data.map((item) => {
      if (item.fj) {
        fjshow.value = true;
        item.fj = JSON.parse(item.fj);
      }
      return item;
    });
    currentRWCount.value = count;
  }
};

const changeYjzb = async (val: string) => {
  const res = await getBBMEJZB(val);
  currentEJZB.value = res.data;
};

const enterinfo = ref();
const onEnterInfo = () => {
  dialogEnterInfo.value = true;
  enterinfo.value = JSON.parse(JSON.stringify(currentBCSM.value));
};

const checkLastYear = () => {
  window.open(
    `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${rrrr.value.oldtaskid}`,
  );
};

const onFy = (uri: string) => {
  const url = uri;
  // window.open(url);
  handOnlineView(url);
};

// handOnlineView 在线查看
const handOnlineView = (file: string) => {
  const onlineViewFile = (url: string) => {
    if (url) {
      // 获取文件格式
      const getFileFormatFun = (getFileFormatFunUrl: string) => {
        const fileFormatStr = getFileFormatFunUrl.slice(
          getFileFormatFunUrl.lastIndexOf(".") + 1,
        );
        return fileFormatStr;
      };

      // if (getFileFormatFun(url) === 'pdf') {
      if (["pdf", "png", "jpg"].includes(getFileFormatFun(url))) {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", url);
        xhr.responseType = "blob";
        xhr.onload = () => {
          console.log(206666, xhr);
          if (xhr.status === 200) {
            let blobType = "";
            if (getFileFormatFun(url) === "pdf") {
              blobType = "application/pdf";
            }
            if (["png", "jpg"].includes(getFileFormatFun(url))) {
              blobType = "image/jpeg";
            }
            const blob = new Blob([xhr.response], { type: blobType });

            const objectURL = window.URL.createObjectURL(blob);

            window.open(objectURL, url, "width=1100,height=768");

            window.URL.revokeObjectURL(objectURL);
          }
        };

        xhr.send();
        return false;
      }

      if (["doc", "docx", "xlsx", "xls"].includes(getFileFormatFun(url))) {
        const officeUrl = `https://view.officeapps.live.com/op/view.aspx?src=${url}`;
        window.open(officeUrl, url, "width=1100,height=768");
      } else {
        window.open(url, url, "width=800,height=700");
      }
    } else {
      console.error("缺少查看文件地址", url);
    }
  };

  onlineViewFile(file);
};

// 下载附件
const downloadAttachment = async (uri: string, fileName?: string) => {
  // 使用fetch API获取文件
  const response = await fetch(uri);

  // 检查响应是否成功
  if (!response.ok) {
    throw new Error("网络响应错误");
  }

  // 获取Blob对象
  const blob = await response.blob();

  // 创建一个指向blob的URL
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  if (fileName) {
    // 如果提供了文件名，则设置下载的文件名
    link.download = fileName;
  }
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const checkPlugin = () => {
  window.open(
    `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?bizid=${checkPluginData.value.guid}`,
  );
};

const sendCompete = () => {
  iframeUrl.value = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?MBRWGUID=${rrrr.value.guid}`;
  completeDialogVisible.value = true;
};

const editCompete = () => {
  iframeUrl.value = `https://ehall.zcmu.edu.cn:5003/iForm/27175042C38A2C890FEE95?MBRWGUID=${rrrr.value.guid}`;
  completeDialogVisible.value = true;
};

const editSubmit = async () => {
  dialogEnterInfo.value = false;
  const { count } = await saveRWBCSM(rrrr.value.guid, enterinfo.value);
  if (count == 1) {
    ElMessage.success("录入补充说明成功！");
    currentBCSM.value = JSON.parse(JSON.stringify(enterinfo.value));
  }
};

const nowdataData = ref<string>("");
const getnowdate = async () => {
  const res = await getNowDate();
  nowdataData.value = res;
};
// 获取当前年度所有已发布任务
const getCurrentYearDepartmentName = async () => {
  formInline.fzrxm = "";
  // 页面跳转时触发
  try {
    loading.value = true;
    if (pppp.value == true) {
      const { data, count } = await getCurrentYearRWByDepartmentName(
        page.value,
        limit.value,
        "",
        "",
        route.query.code as string,
      );
      tableData.value = data || [];
      total.value = count;
    } else {
      if (route.query.ssbm) {
        // 从首页跳转时，清除负责人字段
        formInline.fzrxm = "";
      }
      const requestInfo = {
        yjzb: formInline.yjzb,
        ejzb: formInline.ejzb,
        sjzb: formInline.sjzb,
        zbrw: formInline.zbrw,
        xyzpfbzt: formInline.xyzpfbzt,
        xdbm: formInline.xdbm,
        fzrxm: formInline.fzrxm,
        ssbm: route.query.ssbm ? route.query.ssbm : "",
      };
      const { data, count } = await getCurrentYearRWByDepartmentName(
        page.value,
        limit.value,
        JSON.stringify(requestInfo),
        "",
        route.query.code as string,
      );
      tableData.value = data || [];
      total.value = count;
    }
  } finally {
    loading.value = false;
  }
};

// 修改数字
const onChangeInput = async (val: any, row: any) => {
  const res = await bmtxSaveEvaluate(row.guid, row.xyzp);
  if (res.code === 200 && res.count === 1) {
    ElMessage.success("评分编辑成功");
  }
};

// 页面
// const reportingOnclick = (row: any) => {
//   bgdialogTitle.value = row.sjzb;
//   bgDialogVisible.value = true;
// };

const dialogSubmit = (xyzpAll: any, sjzbnew: any) => {
  let sjzb = sjzbnew;
  tableData.value.forEach((ele) => {
    if (ele.sjzb === sjzb) {
      ele.xyzp = xyzpAll;
    }
  });
};

// 表格
const bgReportingOnclick = (row: any) => {
  window.open(
    // `http://***********:5003/iForm/191103B9A95CF0CB209403?MBRWGUID=${row.guid}`
    `https://ydmh.guangshaxy.com/formWebSite/iForm/191103B9A95CF0CB209403?MBRWGUID=${row.guid}`,
  );
};

const curdep = ref<string>("");
const getCurrentUserDep = async () => {
  const res = await getCurrentUserDepName();
  curdep.value = res;
};

const onClickSpzt = (row: any) => {
  window.open(row.spztlj);
};

const getbrfzrwzb = async () => {
  const { data } = await getbrfzrwzbs();
  brfzrwzb.value = data;
};

const getzbshqks = async () => {
  const { data } = await getzbshqk(curdep.value);
  zbshqk.value = data;
};

// 获取当前登录人菜单权限
const getCurrentMenuPermission = async () => {
  // const { code } = await getCurrentUserMenuPermission();
  let res = await getCurrentUserMenuPermission();
  let code;
  if (window.sessionStorage.getItem("usercode") != null) {
    code = Number(window.sessionStorage.getItem("usercode"));
  } else {
    code = res[0].code;
  }
  usercode.value = code;
};

const onAllBtnClick = async () => {
  // 点击全部按钮时触发，获取所有数据
  onqbClick.value = true;
  formInline.yjzb = "";
  formInline.ejzb = "";
  formInline.sjzb = "";
  formInline.zbrw = "";
  formInline.fzrxm = "";
  formInline.xdbm = "";
  formInline.xyzpfbzt = "";
  try {
    // const requestInfo = "";
    loading.value = true;
    const { data, count } = await getCurrentYearRWByDepartmentName(
      page.value,
      limit.value,
      "",
      "",
      route.query.code as string,
    );
    tableData.value = data;
    total.value = count;
  } finally {
    loading.value = false;
  }
};

// 导出
const exportExcel = () => {
  exportVisible.value = true;
};

// 导出确定
const exportSumbit = () => {
  const obj = {};
  console.log("start");
  checkList.value.forEach((item: any) => {
    obj[item] = true;
  });
  if (usercode.value == 20 || usercode.value == 30 || usercode.value == 50) {
    type.value = "部门";
  } else {
    type.value = "学院";
  }
  const url = `http://***********:7005/api/RWDC/BMExportTargetTask?dczd=${JSON.stringify(
    [obj],
  )}&departmentName=${curdep.value}&type=${type.value}
  &yjzb=${formInline.yjzb}
  &ejzb=${formInline.ejzb}
  &sjzb=${formInline.sjzb}
  &zbrw=${formInline.zbrw}
  &fzrxm=${formInline.fzrxm}
  &xyzpfbzt=${formInline.xyzpfbzt}`;
  console.log(url);
  window.open(url);
  exportVisible.value = false;
};

// 提交评价
const submitEvaluate = async () => {
  console.log(multipleSelection.value.length);
  if (multipleSelection.value.length == 0) {
    ElMessageBox.alert("请至少勾选一条任务！", "信息", {
      confirmButtonText: "确定",
    });
  } else {
    const checkedGUID = multipleSelection.value
      .map((item) => item.guid)
      .join("|");
    const res = await getCurrentUserDepID();
    const url = `https://ydmh.guangshaxy.com/formWebSite/layout/c116cc7d9b0d4bbc8f929f39d74ccb9f/iTask/Process231226154340?taskGUID=${checkedGUID}`;
    window.open(url);
    multipleTableRef.value.clearSelection();
  }
};

const getMRFZR = async () => {
  const res = await getRwmrfzr();
  if (res === true) {
    declarationShow.value = true;
  } else {
    declarationShow.value = false;
  }
};

const getRwmrfz = async () => {
  const res = await getRwmrfzr();
  pppp.value = res;
};

const currentEJZB = ref();
const getCurrentEjzb = async () => {
  formInline.fzrxm = user.value?.profile.UserName;
  const res = await getBBMEJZB("");
  currentEJZB.value = res.data;
};

const currentSsmk = ref();
const getcurrentSsmk = async () => {
  const res = await getBBMSSMK();
  currentSsmk.value = res.data;
};

const xdbmData = ref<GET_COLLEGEINFO_Type[]>([]);
const getAllDepe = async () => {
  const { data } = await getAllDep();
  xdbmData.value = data;
};

const currentYjzb = ref();
const getcurrentyjzb = async () => {
  const res = await getBMYJZB();
  currentYjzb.value = res.data;
};

const handleDisabled = (row: GET_RWFJZT_Type) => {
  if (row.fzrgh) {
    return (
      row.fzrgh.split(",").includes(user.value?.profile.UserId as string) &&
      row.sjrw !== null &&
      row.sfbmtxxyjck === "否"
    );
  } else {
    return false;
  }
};

const handleShowBtn = (row: GET_RWFJZT_Type) => {
  const isFzr = row.fzrgh
    ? row.fzrgh.split(",").includes(user.value?.profile.UserId as string)
    : false;

  return row.sjrw === null && row.sfbmtxxyjck === "是" && !isFzr;
  // if (row.sjrw) {
  //   return false;
  // }

  // if (row.sfbmtxxyjck === "是" && !row.fzrgh) {
  //   return true;
  // }
};

const handleShowBtn2 = (row: GET_RWFJZT_Type) => {
  return (
    row.sjrw === null &&
    row.sfbmtxxyjck === "是" &&
    row.fzrgh &&
    row.fzrgh.split(",").includes(user.value?.profile.UserId as string)
  );

  // if (row.sjrw) {
  //   return false;
  // }

  // if (
  //   row.sfbmtxxyjck === "是" &&
  //   row.fzrgh &&
  //   row.fzrgh.split(",").includes(user?.profile.UserId)
  // ) {
  //   return true;
  // } else {
  //   return false;
  // }
};

const dddd = ref<boolean>(false);
const handleLook = async (row: GET_RWFJZT_Type) => {
  if (row.sfbmtxxyjck === "是") {
    dddd.value = true;
  } else {
    dddd.value = false;
  }

  const { data, msg } = await getCollegeCompletionDetailByTaskId(row.guid);

  if (!data) {
    ElMessage.error(msg);
    return;
  }

  collegeCompletionDetail.value = data;
  collegeCompletionDetailVisible.value = true;
};

const handleSave = async (row) => {
  await saveWCQK([row]);
};

const handleClick = () => {
  const url =
    "https://ydmh.guangshaxy.com/formWebSite/layout/9b930813fe9b4a9dac790fa243449e0e/iTask/Process240322082534";
  window.open(url, "_blank");
};

const handleChange = (value: string) => {
  if (value !== "2024") {
    tableData.value = [];
  } else {
    cxbgData();
  }
};

onMounted(async () => {
  getCurrentMenuPermission();
  getnowdate();
  getMRFZR();
  getCurrentEjzb();
  getcurrentSsmk();
  getcurrentyjzb();
  getAllDepe();
  getbrfzrwzb();
  await getCurrentUserDep();
  await getRwmrfz();
  await getCurrentYearDepartmentName();
  await getzbshqks();
});
</script>

<style lang="less" scoped>
.one-show {
  display: inline-block;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

h2 {
  text-align: center;
}
.el-col-4 {
  border: 1px solid rgb(236, 238, 244);
  height: calc(100vh - 100px);
}
.left-header {
  text-align: center;
}
.header-btn {
  text-align: center;
  margin-bottom: 5px;
}
.connent {
  padding: 10px;
  p {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  span {
    color: rgb(148, 148, 148);
  }
}
.header {
  height: 40px;
  .el-form {
    display: flex;
    // justify-content: space-between;
  }
}

.center {
  float: right;
  margin-right: 80px;
}
.tabledata {
  margin: 2px 20px 10px 0px;
}
.el-row {
  padding: 15px 20px 15px 20px;
}
.fujian {
  margin-top: 10px;
  margin-bottom: 10px;
  .fujian-plugin {
    a {
      margin-top: 5px;
      margin-left: 15px;
      display: block;
      color: #2a59b6;
      width: 200px;
      overflow: hidden; //块元素超出隐藏
      text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
      white-space: nowrap; //规定段落中的文本不进行换行
    }
  }
}

/* 更改背景色 */
:deep(.el-popper) {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
}

.page-fen {
  margin-left: 150px;
}

.el-table {
  height: calc(100vh - 210px);
  border: 1px solid rgb(236, 238, 244);
}

::v-deep .el-table__body .el-table__row.hover-row td {
  background-color: #0ebb9e81 !important;
}

.el-scrollbar {
  height: calc(100vh - 215px);
}

:deep(.el-radio__label) {
  display: none;
}

iframe {
  border: none;
}

:deep(.el-dialog__body) {
  padding: 0 20px 20px 20px;
}

.center-btn {
  display: flex;
  justify-content: space-between;
  margin-right: 20px;
  margin-bottom: 10px;
  .btn-left {
    .top {
      margin-top: 6px;
      margin-bottom: 10px;
      span {
        color: #919398;
        margin-right: 30px;
      }
    }
  }
}
.tag1 {
  background: orange;
  color: #fff;
  margin-right: 5px;
}
.tag2 {
  background: green;
  color: #fff;
  margin-right: 5px;
}

.tag3 {
  background: #3896fb;
  color: #fff;
  margin-right: 5px;
}
</style>

<style>
.dddd {
  background: #f6f6f6 !important;
  color: #000;
  max-width: 18%;
  line-height: 24px;
  border: none;
  font-size: 14px;
}
</style>

import { useUser } from "@/oidc";
import axios from "axios";
import { ElMessage } from "element-plus";

let request = axios.create({
  baseURL: "http://10.18.0.224:7005/", //基础路径
  timeout: 30000, //超时时间设置
});

// request实例添加请求与响应拦截器
request.interceptors.request.use((config) => {
  const user = useUser();
  config.headers.Authorization = `Bearer ${user?.access_token}`;
  return config;
});

//响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 失败回调：处理http网络错误
    let message = "";
    let status = error.response?.status;
    switch (status) {
      case 401:
        message = "token过期";
        break;
      case 403:
        message = "无权访问";
        break;
      case 404:
        message = "请求地址错误";
        break;
      case 500:
        message = "服务器出现错误";
        break;
      default:
        message = "网络出现问题";
        break;
    }
    // 提示错误信息
    ElMessage({
      type: "error",
      message,
    });
    return Promise.reject(error);
  },
);
export default request;

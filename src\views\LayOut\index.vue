<template>
  <div v-if="isAuthenticated" class="layOut">
    <el-container>
      <el-header>
        <HeaderPage />
      </el-header>
      <el-main>
        <MainPage />
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import HeaderPage from "./components/HeaderPage.vue";
import MainPage from "./components/MainPage.vue";
import { unref, watchEffect } from "vue";
import { useAuth } from "vue-oidc-provider";

const { isAuthenticated, signinRedirect, isLoading } = useAuth();

watchEffect(() => {
  if (!unref(isLoading) && !unref(isAuthenticated)) {
    signinRedirect();
  }
});
</script>

<style lang="less" scoped>
.layOut {
  height: 100%;
  .el-header {
    background-color: #135abc;
    padding-right: 0;
  }
}
.el-main {
  padding: 0px;
}
</style>

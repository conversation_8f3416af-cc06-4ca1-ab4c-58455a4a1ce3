<script setup lang="ts">
import { getWJXYWCQK } from "@/api/Home";
import { onMounted, ref } from "vue";
import * as echarts from "echarts";

type EChartsOption = echarts.EChartsOption;

const chartData = ref<Array<GET_WCQK_TYPE>>([]);

const init = async () => {
  const { data } = await getWJXYWCQK();
  chartData.value = data;
  initChart();
};

const initChart = () => {
  var chartDom = document.getElementById("main")!;
  var myChart = echarts.init(chartDom, null, {});
  var option: EChartsOption;

  option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        if (Array.isArray(params)) {
          const name = params[0].name;
          const targetNum = params[0].data || 0;
          const currentNum = params[1].data || 0;
          return (
            `<span style="display:inline-block;margin-bottom:5px;font-weight:600;">${name}</span>` +
            "<br/>" +
            "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#91cc75'></span>" +
            "完成率: " +
            ((Number(currentNum) / Number(targetNum)) * 100).toFixed(2) +
            "%"
          );
        }
        return "";
      },
    },
    yAxis: {
      type: "category",
      data: chartData.value.map((item) => item.bmmc),
    },
    xAxis: {
      type: "value",
    },
    series: [
      {
        data: chartData.value.map((item) => item.zbzs),
        type: "bar",
        itemStyle: {
          color: "#b3b3b3",
        },
        label: {
          show: true,
          position: "insideRight",
        },
      },
      {
        data: chartData.value.map((item) => item.zbwcs),
        type: "bar",
        barGap: "-100%",
        label: {
          show: true,
          position: "insideRight",
        },
      },
    ],
    grid: {
      top: "50px",
      left: "130px",
      right: "50px",
      bottom: "50px",
    },
  };

  option && myChart.setOption(option);

  window.addEventListener("resize", function () {
    myChart.resize();
  });
};

onMounted(() => {
  init();
});
</script>

<template>
  <div class="college-comparison-chart">
    <div id="main"></div>
  </div>
</template>

<style lang="less">
.college-comparison-chart {
  width: 100%;
  height: calc(100% - 50px);

  #main {
    width: 100%;
    height: 500px;
  }
}
</style>

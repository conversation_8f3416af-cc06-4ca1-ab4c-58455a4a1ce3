import request from "@/utils/request";
enum API {
  GET_SELECT_ZNBM_ZBLX = "/api/MBXD/Select_ZNBM_ZBLX",
  GET_ALLZBLX = "/api/MBXD/getALLZBLX",
  GET_AllZBLY = "/api/MBXD/getAllZBLY",
  GET_AllZZRBM = "/api/MBXD/getAllZRBM",
  GET_RW_BYYEAR = "/api/MBXD/getRWByYear",
  UPLOAD_FILE = "/api/MBXD/UploadFile",
}

// 查询所有职能部门和指标类型
export const getAllBmzblx = () =>
  request.get<unknown, Result<Array<GET_ZNBM_ZBLX_Type>>>(
    API.GET_SELECT_ZNBM_ZBLX,
  );

// 查询所有指标类型
export const getALLZBLX = () =>
  request.get<unknown, Result<Array<GET_ALLZBLX_Type>>>(API.GET_ALLZBLX);

// 查询所有指标来源
export const getALLZBLY = () =>
  request.get<unknown, Result<Array<GET_AllZBLY_Type>>>(API.GET_AllZBLY);

// 获取协办部门
export const getALLZRBM = () =>
  request.get<unknown, Result<Array<GET_AllZRBM_Type>>>(API.GET_AllZZRBM);

// 根据年份查询对应年份的所有的任务
export const getRwByYear = (
  year: string,
  page: number,
  limit: number,
  zbrw?: string,
  ssbm?: string,
) =>
  request.get<unknown, Result<Array<SEARCH_YEAR_Type>>>(API.GET_RW_BYYEAR, {
    params: { year, page, limit, zbrw, ssbm },
  });

// 上传文件
export const uploadFile = (file: FormData) => { 
  return request.post<unknown, Result<any>>(
    API.UPLOAD_FILE,
    file,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    },
  );
};
